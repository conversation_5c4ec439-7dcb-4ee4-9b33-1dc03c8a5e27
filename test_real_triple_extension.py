# -*- coding: utf-8 -*-
"""
测试真正的三段式扩展名文件
"""
import os
import subprocess

def create_test_files():
    """创建真正的三段式扩展名测试文件"""
    test_dir = r"d:\1"
    
    # 确保目录存在
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试文件列表：(文件名, 大小MB, 描述, 段数)
    test_files = [
        # 三段式扩展名（3个部分：文件名.扩展名1.扩展名2）
        ("video.mp4.bak", 30, "小三段式 - 不应该被处理", 3),
        ("movie.avi.backup", 70, "大三段式 - 应该被处理", 3),
        ("document.pdf.old", 20, "小三段式 - 不应该被处理", 3),
        ("archive.7z.temp", 80, "大三段式 - 应该被处理", 3),
        
        # 四段式扩展名（4个部分：文件名.扩展名1.扩展名2.扩展名3）
        ("L-test.7z.001.zip", 10, "小四段式 - 应该被处理（不限制大小）", 4),
        ("L-movie.avi.003.mp4", 100, "大四段式 - 应该被处理（不限制大小）", 4),
        
        # 正常文件
        ("normal.txt", 5, "正常文件 - 不应该被处理", 2),
        ("regular.mp4", 200, "正常大文件 - 不应该被处理", 2),
    ]
    
    print(f"在 {test_dir} 中创建测试文件...")
    
    for filename, size_mb, description, segments in test_files:
        file_path = os.path.join(test_dir, filename)
        size_bytes = size_mb * 1024 * 1024
        
        try:
            # 使用 fsutil 创建指定大小的文件
            result = subprocess.run([
                "fsutil", "file", "createnew", file_path, str(size_bytes)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  ✓ {segments}段式: {filename} ({size_mb}MB) - {description}")
            else:
                print(f"  ✗ 创建文件失败: {filename} - {result.stderr}")
                
        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

def analyze_filename(filename):
    """分析文件名结构"""
    parts = filename.split('.')
    segment_count = len(parts)
    
    # 四段式：4个或更多部分，且最后三个都是2-4字符
    is_four_part = False
    if segment_count >= 4:
        last_three = parts[-3:]
        is_four_part = all(2 <= len(part) <= 4 for part in last_three)
    
    # 三段式：正好3个部分
    is_three_part = (segment_count == 3)
    
    return segment_count, is_four_part, is_three_part

def test_extension_logic():
    """测试扩展名处理逻辑"""
    test_files = [
        # 三段式扩展名
        ("video.mp4.bak", 30),
        ("movie.avi.backup", 70),
        ("document.pdf.old", 20),
        ("archive.7z.temp", 80),
        
        # 四段式扩展名
        ("L-test.7z.001.zip", 10),
        ("L-movie.avi.003.mp4", 100),
        
        # 正常文件
        ("normal.txt", 5),
        ("regular.mp4", 200),
    ]
    
    print(f"\n{'='*80}")
    print("测试扩展名处理逻辑")
    print(f"{'='*80}")
    
    for filename, size_mb in test_files:
        print(f"\n测试文件: {filename} ({size_mb}MB)")
        
        segment_count, is_four_part, is_three_part = analyze_filename(filename)
        
        # 模拟 os.path.splitext
        base_filename, ext = os.path.splitext(filename)
        
        # 检查扩展名是否在已知列表中
        known_extensions = {
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
            '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
            '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
        }
        
        is_unknown_extension = (not ext or ext.lower() not in known_extensions)
        
        print(f"  段数: {segment_count}")
        print(f"  四段式扩展名: {is_four_part}")
        print(f"  三段式扩展名: {is_three_part}")
        print(f"  最后扩展名 '{ext}': {'未知' if is_unknown_extension else '已知'}")
        print(f"  文件大小: {size_mb}MB")
        
        # 根据新的逻辑判断处理结果
        if is_four_part:
            print(f"  → 会被四段式扩展名逻辑处理（不限制大小）")
        elif is_three_part and size_mb > 50:
            print(f"  → 会被三段式扩展名逻辑处理（大于50MB）")
        elif is_three_part and size_mb <= 50:
            print(f"  → 不会被处理（三段式但小于等于50MB）")
        elif is_unknown_extension and size_mb > 500:
            print(f"  → 会被未知扩展名逻辑处理（大于500MB）")
        else:
            print(f"  → 不会被处理（正常文件或不满足条件）")

if __name__ == "__main__":
    print("真正的三段式扩展名测试")
    print("=" * 50)
    
    create_test_files()
    test_extension_logic()
    
    print(f"\n{'='*80}")
    print("测试总结：")
    print("- 四段式扩展名：不限制文件大小，直接处理")
    print("- 三段式扩展名：需要大于50MB才处理")
    print("- 未知扩展名：需要大于500MB才处理")
    print("- 正常文件：不处理")
    print(f"{'='*80}")
