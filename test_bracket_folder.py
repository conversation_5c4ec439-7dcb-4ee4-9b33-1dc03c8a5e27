# -*- coding: utf-8 -*-
"""
测试方括号文件夹是否影响文件遍历
"""
import os
import tempfile
import shutil

def test_bracket_folder():
    """测试包含方括号的文件夹是否能正常遍历"""
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"临时目录: {temp_dir}")
    
    try:
        # 创建包含方括号的文件夹
        bracket_folder = os.path.join(temp_dir, "test[folder]name")
        os.makedirs(bracket_folder)
        print(f"创建方括号文件夹: {bracket_folder}")
        
        # 在方括号文件夹中创建测试文件
        test_files = [
            "L-叛逆.7z.005.avi",
            "normal_file.txt",
            "test.pdf"
        ]
        
        for filename in test_files:
            file_path = os.path.join(bracket_folder, filename)
            with open(file_path, 'w') as f:
                f.write("test content")
            print(f"创建文件: {file_path}")
        
        print("\n=== 测试 os.listdir() ===")
        try:
            files = os.listdir(bracket_folder)
            print(f"找到文件: {files}")
        except Exception as e:
            print(f"os.listdir() 错误: {e}")
        
        print("\n=== 测试 os.walk() ===")
        try:
            for root, dirs, files in os.walk(temp_dir):
                print(f"目录: {root}")
                print(f"子目录: {dirs}")
                print(f"文件: {files}")
        except Exception as e:
            print(f"os.walk() 错误: {e}")
        
        print("\n=== 测试文件路径构建 ===")
        try:
            for filename in test_files:
                file_path = os.path.join(bracket_folder, filename)
                exists = os.path.exists(file_path)
                print(f"文件 {filename}: 路径={file_path}, 存在={exists}")
                
                if exists:
                    size = os.path.getsize(file_path)
                    print(f"  文件大小: {size} 字节")
        except Exception as e:
            print(f"文件路径测试错误: {e}")
            
        print("\n=== 测试 os.path.splitext() ===")
        for filename in test_files:
            base, ext = os.path.splitext(filename)
            print(f"文件 {filename}: base='{base}', ext='{ext}'")
    
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            print(f"\n清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")

def test_current_folder():
    """测试当前目录中是否有方括号文件夹"""
    current_dir = os.getcwd()
    print(f"\n=== 检查当前目录: {current_dir} ===")
    
    try:
        items = os.listdir(current_dir)
        bracket_folders = [item for item in items if '[' in item and ']' in item and os.path.isdir(item)]
        
        if bracket_folders:
            print(f"发现包含方括号的文件夹: {bracket_folders}")
            
            for folder in bracket_folders:
                folder_path = os.path.join(current_dir, folder)
                print(f"\n检查文件夹: {folder_path}")
                
                try:
                    files = os.listdir(folder_path)
                    print(f"  文件数量: {len(files)}")
                    
                    # 检查是否有大文件
                    large_files = []
                    for filename in files:
                        file_path = os.path.join(folder_path, filename)
                        if os.path.isfile(file_path):
                            try:
                                size = os.path.getsize(file_path)
                                size_mb = size / (1024 * 1024)
                                if size_mb > 100:  # 大于100MB
                                    large_files.append((filename, size_mb))
                            except OSError:
                                pass
                    
                    if large_files:
                        print(f"  大文件 (>100MB):")
                        for filename, size_mb in large_files:
                            print(f"    {filename}: {size_mb:.1f}MB")
                    else:
                        print(f"  没有发现大文件")
                        
                except Exception as e:
                    print(f"  访问文件夹失败: {e}")
        else:
            print("当前目录中没有包含方括号的文件夹")
            
    except Exception as e:
        print(f"检查当前目录失败: {e}")

if __name__ == "__main__":
    print("测试方括号文件夹对文件遍历的影响")
    print("=" * 50)
    
    test_bracket_folder()
    test_current_folder()
