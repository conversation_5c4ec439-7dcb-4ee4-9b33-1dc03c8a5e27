#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件整理工具
功能：整理文件夹中的视频、图片和其他文件
作者：AI Assistant
"""

import os
import json
import shutil
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
from pathlib import Path
import threading
from datetime import datetime
import hashlib
import csv
import re

class FileOrganizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("文件整理工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 配置变量
        self.selected_folder = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="请选择要整理的文件夹")

        # 用户偏好设置
        self.user_preferences = {}

        # 存储下拉框引用以便联动更新
        self.combo_boxes = {}
        
        # 文件类型定义
        self.video_extensions = {'.mp4', '.avi', '.mkv', '.ts', '.mov', '.wmv', '.flv', '.webm', '.m4v'}
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.ico', '.raw', '.svg'}

        # 图片处理设置
        self.image_settings = {
            'min_images_for_folder': 1,  # 多少张图片以上才认为是图片文件夹
            'max_single_image_size': 10 * 1024 * 1024,  # 单张图片超过多少MB单独处理 (10MB)
            'image_folder_action': 'keep',  # 图片文件夹默认处理方式
            'single_image_action': 'move_to_local_image',  # 单张图片默认处理方式
            'large_image_action': 'move_to_image',  # 大图片默认处理方式
            'image_quality_check': True,  # 是否检查图片质量
            'min_image_resolution': 100,  # 最小图片分辨率 (像素)
            'delete_small_images': False,  # 是否删除过小的图片
            'group_by_date': False,  # 是否按日期分组图片
            'preserve_folder_structure': True  # 是否保持原有文件夹结构
        }

        # 文件名规范化设置
        self.normalize_settings = {
            'enable_normalization': True,  # 启用文件名规范化
            'min_length': 25,  # 最小文件名长度
            'max_length': 42,  # 最大文件名长度
            'auto_handle_duplicates': True,  # 自动处理重名
            'preserve_extensions': True,  # 保持扩展名
            'smart_truncate': True,  # 智能截断（在标点处）
            'log_normalize_operations': True,  # 记录规范化操作
            'replacements_file': 'replacements.csv',  # 替换规则文件
            'deletions_file': 'deletions.csv'  # 删除规则文件
        }

        # 操作记录
        self.operation_log = []
        self.log_file = "file_organizer_log.json"

        # 预览窗口相关
        self.preview_window = None
        self.preview_operations = []
        self.modified_operations = []
        self.current_preview_index = 0
        self.preview_results = []  # 存储每个文件夹的预览结果
        self.original_actions = {}  # 存储每个文件夹的原始设置，用于撤销

        # 工具提示相关
        self.tooltip = None
        self.tooltip_item = None

        # 创建界面
        self.create_widgets()
        
        # 加载之前的操作记录
        self.load_operation_log()

        # 加载用户偏好设置
        self.load_user_preferences()

        # 初始化主界面返回按钮状态
        self.root.after(100, self.update_main_back_button_state)

    def update_combo_selection(self, operation, file_type, new_value):
        """更新指定文件类型的下拉框选择"""
        try:
            combo_key = f"{operation.get('folder_name', 'unknown')}_{file_type}"
            if combo_key in self.combo_boxes:
                combo_info = self.combo_boxes[combo_key]
                # 找到对应的显示文本
                for display, value in combo_info['options']:
                    if value == new_value:
                        combo_info['action_var'].set(display)
                        break
        except Exception as e:
            # 忽略更新错误
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 文件夹选择区域
        folder_frame = ttk.LabelFrame(main_frame, text="选择文件夹", padding="5")
        folder_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        folder_frame.columnconfigure(1, weight=1)
        
        ttk.Label(folder_frame, text="目标文件夹:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        folder_entry = ttk.Entry(folder_frame, textvariable=self.selected_folder, state="readonly")
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

        # 添加返回上级按钮
        self.main_back_button = ttk.Button(folder_frame, text="↑ 返回上级",
                                          command=self.go_back_to_parent_from_main)
        self.main_back_button.grid(row=0, column=2, padx=(0, 5))

        ttk.Button(folder_frame, text="浏览", command=self.select_folder).grid(row=0, column=3)
        
        # 功能按钮区域
        button_frame = ttk.LabelFrame(main_frame, text="操作功能", padding="5")
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 第一行按钮
        button_row1 = ttk.Frame(button_frame)
        button_row1.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.start_button = ttk.Button(button_row1, text="开始整理", command=self.start_organizing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.preview_button = ttk.Button(button_row1, text="手动整理", command=self.show_preview_window)
        self.preview_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(button_row1, text="停止", command=self.stop_organizing, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 第二行按钮
        button_row2 = ttk.Frame(button_frame)
        button_row2.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.restore_button = ttk.Button(button_row2, text="还原操作", command=self.restore_operations)
        self.restore_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_log_button = ttk.Button(button_row2, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT, padx=(0, 10))

        self.exit_button = ttk.Button(button_row2, text="退出", command=self.root.quit)
        self.exit_button.pack(side=tk.RIGHT)
        
        # 进度显示区域
        progress_frame = ttk.LabelFrame(main_frame, text="进度信息", padding="5")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 状态标签
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(3, weight=1)
        
        # 运行状态
        self.is_running = False
        self.should_stop = False
    
    def select_folder(self):
        """选择文件夹"""
        folder = filedialog.askdirectory(title="选择要整理的文件夹")
        if folder:
            self.selected_folder.set(folder)
            self.log_message(f"已选择文件夹: {folder}")
            # 更新主界面返回按钮状态
            self.update_main_back_button_state()
    
    def log_message(self, message):
        """在日志区域显示消息（线程安全）"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        try:
            # 检查是否在主线程中
            import threading
            if threading.current_thread() is threading.main_thread():
                # 主线程，直接更新UI
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)
                self.root.update_idletasks()
            else:
                # 子线程，使用after方法安全更新UI
                self.root.after(0, self._update_log_ui, log_entry)
        except Exception:
            # 如果UI更新失败，至少打印到控制台
            print(log_entry.strip())

    def _update_log_ui(self, log_entry):
        """在主线程中更新日志UI"""
        try:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.root.update_idletasks()
        except Exception:
            # 如果仍然失败，打印到控制台
            print(log_entry.strip())
    
    def start_organizing(self):
        """开始整理文件"""
        if not self.selected_folder.get():
            messagebox.showwarning("警告", "请先选择要整理的文件夹！")
            return
        
        if not os.path.exists(self.selected_folder.get()):
            messagebox.showerror("错误", "选择的文件夹不存在！")
            return
        
        # 确认对话框
        result = messagebox.askyesno("确认",
            "开始整理文件？\n\n整理规则：\n"
            "- 以各子文件夹为独立单位进行整理\n"
            "- 视频文件移动到各子文件夹的'视频'文件夹\n"
            "- 包含多个图片的文件夹移动到'图片'文件夹\n"
            "- 其他文件移动到各子文件夹的'其他'文件夹\n"
            "- 小于100KB的文件将被删除\n"
            "- 空文件夹将被清理\n"
            "- 所有操作都会被记录，可以还原")
        
        if result:
            self.is_running = True
            self.should_stop = False
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            
            # 在新线程中执行整理操作
            thread = threading.Thread(target=self.organize_files)
            thread.daemon = True
            thread.start()
    
    def stop_organizing(self):
        """停止整理操作"""
        self.should_stop = True
        self.log_message("正在停止操作...")
    
    def show_preview_window(self):
        """显示手动整理窗口"""
        if not self.selected_folder.get():
            messagebox.showwarning("警告", "请先选择要整理的文件夹！")
            return

        if not os.path.exists(self.selected_folder.get()):
            messagebox.showerror("错误", "选择的文件夹不存在！")
            return

        self.log_message("开始生成手动整理预览...")

        # 在新线程中生成预览数据
        thread = threading.Thread(target=self.generate_preview_data)
        thread.daemon = True
        thread.start()

    def generate_preview_data(self):
        """生成预览数据 - 直接处理选择的根文件夹"""
        try:
            root_path = self.selected_folder.get()

            # 直接扫描根文件夹的文件
            folder_files = self.scan_folder_files(root_path)

            self.preview_operations = []
            self.preview_results = []
            self.current_preview_index = 0

            # 处理根文件夹本身
            folder_name = os.path.basename(root_path)
            if not folder_name:  # 如果是根目录，使用完整路径
                folder_name = root_path

            # 分类文件
            if folder_files:
                video_files, image_folders, image_files, other_files, small_files = self.classify_folder_files(folder_files, root_path)
            else:
                # 空文件夹的情况
                video_files = []
                image_folders = []
                image_files = []
                other_files = []
                small_files = []
                self.log_message(f"文件夹 '{folder_name}' 为空或无可访问文件")

            # 使用默认操作设置
            default_actions = {
                'video_action': 'move_to_local_video',
                'image_action': 'keep',
                'image_file_action': 'move_to_local_image',
                'other_action': 'keep',
                'small_action': 'delete',
                'create_subfolders': 'never'
            }

            # 尝试应用用户偏好设置
            user_actions = self.apply_user_preferences(root_path, default_actions.copy())

            operation = {
                'folder_path': root_path,
                'folder_name': folder_name,
                'video_files': video_files,
                'image_folders': image_folders,
                'image_files': image_files,
                'other_files': other_files,
                'small_files': small_files,
                'suggested_actions': default_actions,
                'user_actions': user_actions
            }

            # 保存原始设置用于撤销
            self.original_actions[root_path] = default_actions.copy()

            self.preview_operations.append(operation)

            self.log_message(f"预览数据生成完成，处理文件夹: {folder_name}")
            self.log_message(f"  📹 视频文件: {len(video_files)} 个")
            self.log_message(f"  🖼️ 图片文件夹: {len(image_folders)} 个")
            self.log_message(f"  🖼️ 图片文件: {len(image_files)} 个")
            self.log_message(f"  📄 其他文件: {len(other_files)} 个")
            self.log_message(f"  🗑️ 小文件: {len(small_files)} 个")

            # 在主线程中显示预览窗口
            self.root.after(0, self.create_single_folder_preview)

            # 检查空文件夹
            self.root.after(100, lambda: self.check_empty_folders_in_preview(root_path))

            # 更新返回按钮状态
            self.root.after(200, self.update_back_button_state)

        except Exception as e:
            self.log_message(f"生成预览数据时出错: {str(e)}")

    def check_empty_folders_in_preview(self, root_path):
        """在预览生成时检查空文件夹"""
        try:
            # 查找所有空文件夹
            empty_folders = []
            for root, _, _ in os.walk(root_path, topdown=False):
                # 跳过根目录
                if root == root_path:
                    continue

                # 检查文件夹是否为空
                try:
                    if not os.listdir(root):
                        empty_folders.append(root)
                except OSError:
                    # 无法访问文件夹
                    pass

            # 如果有空文件夹，显示提示信息并询问是否删除
            if empty_folders:
                folder_names = [os.path.relpath(f, self.selected_folder.get()) for f in empty_folders]
                folder_list = '\n'.join(f"• {name}" for name in folder_names[:10])  # 最多显示10个
                if len(empty_folders) > 10:
                    folder_list += f"\n... 还有 {len(empty_folders) - 10} 个文件夹"

                # 记录发现的空文件夹
                self.log_message(f"📁 发现 {len(empty_folders)} 个空文件夹")
                for folder_name in folder_names:
                    self.log_message(f"  • {folder_name}")

                # 弹出确认对话框
                result = messagebox.askyesno(
                    "发现空文件夹",
                    f"在手动整理预览中发现 {len(empty_folders)} 个空文件夹：\n\n{folder_list}\n\n"
                    f"这些空文件夹在整理完成后会被自动清理。\n\n"
                    f"是否现在就删除这些空文件夹？\n"
                    f"（选择'否'将在整理完成后自动删除）",
                    icon='question'
                )

                if result:
                    # 用户选择立即删除
                    self.clean_empty_folders(root_path, prompt_user=False)
                    self.log_message("✅ 已立即清理空文件夹")
                    # 刷新目录结构
                    self.refresh_folder_structure()
                else:
                    self.log_message("📋 空文件夹将在整理完成后自动清理")

        except Exception as e:
            self.log_message(f"检查预览空文件夹时出错: {str(e)}")





















    def create_single_folder_preview(self):
        """创建单个文件夹的预览窗口"""
        if self.current_preview_index >= len(self.preview_operations):
            # 所有文件夹都预览完成，显示汇总并开始整理
            self.show_preview_summary()
            return

        current_operation = self.preview_operations[self.current_preview_index]

        # 如果窗口已存在，只更新内容而不重新创建窗口
        if self.preview_window and self.preview_window.winfo_exists():
            # 更新窗口标题，显示完整路径
            current_path = self.selected_folder.get()
            self.preview_window.title(f"手动整理 - {current_path} - {current_operation['folder_name']} ({self.current_preview_index + 1}/{len(self.preview_operations)})")
            # 清除现有内容
            for widget in self.preview_window.winfo_children():
                widget.destroy()
            # 清空之前的下拉框引用
            self.combo_boxes.clear()
        else:
            # 创建新窗口
            self.preview_window = tk.Toplevel(self.root)
            current_path = self.selected_folder.get()
            self.preview_window.title(f"手动整理 - {current_path} - {current_operation['folder_name']} ({self.current_preview_index + 1}/{len(self.preview_operations)})")
            self.preview_window.geometry("1200x800")
            self.preview_window.resizable(True, True)
            # 居中显示
            self.center_preview_window()

        # 创建主框架
        main_frame = ttk.Frame(self.preview_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 15))

        # 文件夹名称和进度
        folder_title = ttk.Label(title_frame, text=f"📁 {current_operation['folder_name']}",
                                font=('Arial', 14, 'bold'))
        folder_title.pack(side=tk.LEFT)

        progress_label = ttk.Label(title_frame, text=f"({self.current_preview_index + 1}/{len(self.preview_operations)})",
                                  foreground='blue')
        progress_label.pack(side=tk.RIGHT)

        # 文件夹路径
        path_label = ttk.Label(main_frame, text=f"路径: {current_operation['folder_path']}",
                              foreground='gray')
        path_label.pack(anchor=tk.W, pady=(0, 10))

        # 创建主要内容区域（左右分栏）
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 左侧：目录树和拖拽区域
        left_frame = ttk.LabelFrame(content_frame, text="目录结构预览", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 右侧：文件统计和操作设置（设置固定宽度确保完全显示）
        right_frame = ttk.Frame(content_frame, width=300)  # 设置固定宽度300像素
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)  # 防止子组件改变框架大小

        # 文件统计区域（右侧上部）
        stats_frame = ttk.LabelFrame(right_frame, text="文件统计", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))

        video_count = len(current_operation['video_files'])
        image_count = len(current_operation['image_folders'])
        image_file_count = len(current_operation.get('image_files', []))  # 新增：单独图片文件统计
        other_count = len(current_operation['other_files'])
        small_count = len(current_operation['small_files'])

        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)

        ttk.Label(stats_grid, text="📹 视频文件:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_grid, text=f"{video_count} 个").grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(stats_grid, text="🖼️ 图片文件夹:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_grid, text=f"{image_count} 个").grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(stats_grid, text="🖼️ 图片文件:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_grid, text=f"{image_file_count} 个").grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(stats_grid, text="📄 其他文件:", font=('Arial', 10, 'bold')).grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_grid, text=f"{other_count} 个").grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(stats_grid, text="🗑️ 小文件:", font=('Arial', 10, 'bold')).grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Label(stats_grid, text=f"{small_count} 个").grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 操作设置区域（右侧中部）
        action_frame = ttk.LabelFrame(right_frame, text="整理设置", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 10))

        # 文件名规范化设置区域（右侧下部）
        norm_frame = ttk.LabelFrame(right_frame, text="📝 文件名规范化", padding="10")
        norm_frame.pack(fill=tk.X)





        # 创建目录树
        self.create_directory_tree(left_frame, current_operation)

        # 为每种文件类型创建操作选择
        self.create_single_action_selector(action_frame, current_operation, 'video', '视频文件', video_count)
        self.create_single_action_selector(action_frame, current_operation, 'image', '图片文件夹', image_count)
        self.create_single_action_selector(action_frame, current_operation, 'image_file', '图片文件', image_file_count)  # 新增：单独图片文件
        self.create_single_action_selector(action_frame, current_operation, 'other', '其他文件', other_count)
        self.create_single_action_selector(action_frame, current_operation, 'small', '小文件', small_count)

        # 在整理设置区域内部添加操作按钮
        self.create_action_buttons_inside_settings(action_frame, current_operation)

        # 在规范化区域添加规范化设置
        try:
            self.create_normalization_settings_in_frame(norm_frame, current_operation, self.current_preview_index)
        except Exception as e:
            import traceback
            traceback.print_exc()



        # 确保窗口保持焦点
        self.preview_window.focus_force()
        self.preview_window.lift()

    def create_preview_window(self):
        """创建可交互的预览窗口"""
        if self.preview_window:
            self.preview_window.destroy()

        self.preview_window = tk.Toplevel(self.root)
        current_path = self.selected_folder.get()
        self.preview_window.title(f"手动整理 - {current_path} - 可拖拽调整")
        self.preview_window.geometry("1000x700")
        self.preview_window.resizable(True, True)

        # 居中显示
        self.center_preview_window()

        # 创建主框架
        main_frame = ttk.Frame(self.preview_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题和说明
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text="手动整理 - 拖拽调整操作",
                 font=('Arial', 12, 'bold')).pack(side=tk.LEFT)

        ttk.Label(title_frame, text="💡 提示：可以拖拽文件到不同分类进行整理，双击文件夹可切换根目录",
                 foreground='blue').pack(side=tk.RIGHT)

        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 为每个文件夹创建预览项
        for i, operation in enumerate(self.preview_operations):
            self.create_folder_preview_item(scrollable_frame, operation, i)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="应用更改并开始整理",
                  command=self.apply_preview_changes).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="重置为默认设置",
                  command=self.reset_to_suggestions).pack(side=tk.LEFT, padx=(0, 10))

        # 添加返回上级按钮
        self.back_button = ttk.Button(button_frame, text="↑ 返回上级",
                                     command=self.go_back_to_parent)
        self.back_button.pack(side=tk.LEFT, padx=(10, 0))

        # 检查是否可以返回上级（如果当前不是最初选择的文件夹）
        self.update_back_button_state()

        ttk.Button(button_frame, text="取消",
                  command=self.close_preview_window).pack(side=tk.RIGHT)

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def center_preview_window(self):
        """将预览窗口居中显示"""
        self.preview_window.update_idletasks()
        width = self.preview_window.winfo_width()
        height = self.preview_window.winfo_height()
        x = (self.preview_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.preview_window.winfo_screenheight() // 2) - (height // 2)
        self.preview_window.geometry(f"{width}x{height}+{x}+{y}")

    def create_folder_preview_item(self, parent, operation, index):
        """为单个文件夹创建预览项"""
        folder_frame = ttk.LabelFrame(parent, text=f"📁 {operation['folder_name']}", padding="10")
        folder_frame.pack(fill=tk.X, pady=(0, 10))

        # 文件统计信息
        stats_frame = ttk.Frame(folder_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        video_count = len(operation['video_files'])
        image_folder_count = len(operation['image_folders'])
        image_file_count = len(operation.get('image_files', []))
        other_count = len(operation['other_files'])
        small_count = len(operation['small_files'])

        ttk.Label(stats_frame, text=f"📹 视频: {video_count}").pack(side=tk.LEFT, padx=(0, 15))
        if image_folder_count > 0:
            # 计算图片文件夹中的图片总数
            total_images_in_folders = sum(len(folder['image_files']) for folder in operation['image_folders'])
            ttk.Label(stats_frame, text=f"🖼️ 图片文件夹: {image_folder_count}个({total_images_in_folders}张)").pack(side=tk.LEFT, padx=(0, 15))
        # 始终显示图片文件统计（包含所有图片）
        ttk.Label(stats_frame, text=f"🖼️ 图片文件: {image_file_count}").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Label(stats_frame, text=f"📄 其他: {other_count}").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Label(stats_frame, text=f"🗑️ 小文件: {small_count}").pack(side=tk.LEFT, padx=(0, 15))

        # 操作选择区域
        action_frame = ttk.Frame(folder_frame)
        action_frame.pack(fill=tk.X)

        # 为每种文件类型创建操作选择
        self.create_action_selector(action_frame, operation, index, 'video', '视频文件', video_count)
        if image_folder_count > 0:
            self.create_action_selector(action_frame, operation, index, 'image', '图片文件夹', image_folder_count)
        # 始终显示图片文件操作选择器（包含所有图片）
        self.create_action_selector(action_frame, operation, index, 'image_file', '图片文件', image_file_count)
        self.create_action_selector(action_frame, operation, index, 'other', '其他文件', other_count)
        self.create_action_selector(action_frame, operation, index, 'small', '小文件', small_count)

        # 添加规范化设置区域
        try:
            self.create_normalization_settings_for_folder(folder_frame, operation, index)
        except Exception as e:
            import traceback
            traceback.print_exc()



    def create_action_selector(self, parent, operation, _, file_type, display_name, count):
        """创建文件类型的操作选择器"""
        if count == 0:
            return

        row_frame = ttk.Frame(parent)
        row_frame.pack(fill=tk.X, pady=2)

        # 文件类型标签
        ttk.Label(row_frame, text=f"{display_name}:", width=12).pack(side=tk.LEFT)

        # 操作选择下拉框
        action_var = tk.StringVar()
        current_action = operation['user_actions'].get(f'{file_type}_action', 'auto')

        if file_type == 'small':
            options = [('删除', 'delete'), ('保留在原位置', 'keep'), ('移动到其他', 'move_to_other')]
        elif file_type == 'image_file':
            options = [
                ('移动到本层图片文件夹', 'move_to_local_image'),
                ('移动到图片文件夹', 'move_to_image'),
                ('移动到根目录', 'move_to_root'),
                ('保留在原位置', 'keep')
            ]
        else:
            options = [
                ('保留在原位置', 'keep'),
                ('移动到视频文件夹', 'move_to_video'),
                ('移动到图片文件夹', 'move_to_image'),
                ('移动到其他文件夹', 'move_to_other')
            ]

        # 设置当前值
        for display, value in options:
            if value == current_action:
                action_var.set(display)
                break
        else:
            action_var.set(options[0][0])

        combo = ttk.Combobox(row_frame, textvariable=action_var, values=[opt[0] for opt in options],
                           state="readonly", width=20)
        combo.pack(side=tk.LEFT, padx=(5, 0))

        # 绑定变更事件
        def on_action_change(_):
            selected_display = action_var.get()
            for display, value in options:
                if display == selected_display:
                    operation['user_actions'][f'{file_type}_action'] = value
                    break

        combo.bind('<<ComboboxSelected>>', on_action_change)

    def create_single_action_selector(self, parent, operation, file_type, display_name, count):
        """为单个文件夹创建文件类型的操作选择器"""
        if count == 0:
            return

        row_frame = ttk.Frame(parent)
        row_frame.pack(fill=tk.X, pady=8)

        # 文件类型标签
        type_label = ttk.Label(row_frame, text=f"{display_name}:", width=15, font=('Arial', 10, 'bold'))
        type_label.pack(side=tk.LEFT)

        # 文件数量标签
        count_label = ttk.Label(row_frame, text=f"({count} 个)", foreground='gray')
        count_label.pack(side=tk.LEFT, padx=(5, 15))

        # 操作选择下拉框
        action_var = tk.StringVar()
        current_action = operation['user_actions'].get(f'{file_type}_action', 'auto')

        # 根据文件类型提供合适的选项
        if file_type == 'small':
            options = [
                ('删除', 'delete'),
                ('保留在原位置', 'keep'),
                ('移动到根目录', 'move_to_root'),
                ('移动到其他文件夹', 'move_to_other')
            ]
        elif file_type == 'video':
            options = [
                ('移动到本层视频文件夹', 'move_to_local_video'),
                ('保留在原位置', 'keep'),
                ('移动到根目录', 'move_to_root'),
                ('移动到视频文件夹', 'move_to_video'),
                ('移动到其他文件夹', 'move_to_other')
            ]
        elif file_type == 'image':
            options = [
                ('跳过', 'skip'),
                ('保留在原位置', 'keep'),
                ('移动到根目录', 'move_to_root'),
                ('移动到图片文件夹', 'move_to_image'),
                ('移动到其他文件夹', 'move_to_other')
            ]
        elif file_type == 'image_file':
            options = [
                ('跳过', 'skip'),
                ('移动到本层图片文件夹', 'move_to_local_image'),
                ('移动到图片文件夹', 'move_to_image'),
                ('移动到根目录', 'move_to_root'),
                ('保留在原位置', 'keep')
            ]
        elif file_type == 'other':
            options = [
                ('保留在原位置', 'keep'),
                ('移动到根目录', 'move_to_root'),
                ('移动到其他文件夹', 'move_to_other'),
                ('移动到视频文件夹', 'move_to_video'),
                ('移动到图片文件夹', 'move_to_image'),
                ('删除', 'delete')
            ]
        else:
            # 默认选项（兼容性）
            options = [
                ('保留在原位置', 'keep'),
                ('移动到根目录', 'move_to_root'),
                ('移动到视频文件夹', 'move_to_video'),
                ('移动到图片文件夹', 'move_to_image'),
                ('移动到其他文件夹', 'move_to_other')
            ]

        # 设置当前值
        for display, value in options:
            if value == current_action:
                action_var.set(display)
                break
        else:
            action_var.set(options[0][0])

        combo = ttk.Combobox(row_frame, textvariable=action_var, values=[opt[0] for opt in options],
                           state="readonly", width=25)
        combo.pack(side=tk.LEFT, padx=(0, 10))

        # 存储下拉框引用以便联动更新
        combo_key = f"{operation.get('folder_name', 'unknown')}_{file_type}"
        self.combo_boxes[combo_key] = {
            'combo': combo,
            'action_var': action_var,
            'options': options
        }

        # 绑定变更事件
        def on_action_change(_):
            selected_display = action_var.get()
            for display, value in options:
                if display == selected_display:
                    operation['user_actions'][f'{file_type}_action'] = value

                    # 图片文件夹和图片文件的二选一逻辑
                    if file_type == 'image' and value != 'skip':
                        # 如果选择了图片文件夹的处理方式（非跳过），则图片文件自动设为跳过
                        operation['user_actions']['image_file_action'] = 'skip'
                        self.log_message("图片文件夹已选择处理方式，图片文件自动设为跳过")
                        # 更新图片文件的下拉框显示
                        self.update_combo_selection(operation, 'image_file', 'skip')
                    elif file_type == 'image_file' and value != 'skip':
                        # 如果选择了图片文件的处理方式（非跳过），则图片文件夹自动设为跳过
                        operation['user_actions']['image_action'] = 'skip'
                        self.log_message("图片文件已选择处理方式，图片文件夹自动设为跳过")
                        # 更新图片文件夹的下拉框显示
                        self.update_combo_selection(operation, 'image', 'skip')

                    # 自动刷新预览树
                    if hasattr(self, 'preview_tree'):
                        self.refresh_preview_tree(operation)
                    break

        combo.bind('<<ComboboxSelected>>', on_action_change)

        # 添加说明文本
        if file_type == 'small':
            desc_text = "小于100KB的文件"
        elif file_type == 'image':
            desc_text = "包含多张图片的文件夹"
        elif file_type == 'video':
            desc_text = "视频格式文件"
        else:
            desc_text = "其他格式文件"

        desc_label = ttk.Label(row_frame, text=desc_text, foreground='gray', font=('Arial', 9))
        desc_label.pack(side=tk.LEFT, padx=(10, 0))

    def create_directory_tree(self, parent, operation):
        """创建可拖拽的目录树"""
        # 创建树形控件框架
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 创建双栏布局：当前结构 | 预览结构
        current_frame = ttk.LabelFrame(tree_frame, text="当前结构", padding="5")
        current_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        preview_frame = ttk.LabelFrame(tree_frame, text="整理后预览", padding="5")
        preview_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 当前结构树 - 平衡方案：合理列宽 + 水平滚动条
        current_tree_container = ttk.Frame(current_frame)
        current_tree_container.pack(fill=tk.BOTH, expand=True)

        self.current_tree = ttk.Treeview(current_tree_container, height=15, selectmode='extended')

        # 恢复默认配置：让列宽自动适应，不强制设置宽度
        self.current_tree.configure(show='tree')
        # 移除固定宽度设置，让Treeview自动适应布局

        # 垂直滚动条
        current_v_scrollbar = ttk.Scrollbar(current_tree_container, orient=tk.VERTICAL, command=self.current_tree.yview)
        self.current_tree.configure(yscrollcommand=current_v_scrollbar.set)

        # 水平滚动条
        current_h_scrollbar = ttk.Scrollbar(current_tree_container, orient=tk.HORIZONTAL, command=self.current_tree.xview)
        self.current_tree.configure(xscrollcommand=current_h_scrollbar.set)

        # 使用grid布局
        self.current_tree.grid(row=0, column=0, sticky='nsew')
        current_v_scrollbar.grid(row=0, column=1, sticky='ns')
        current_h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置权重
        current_tree_container.grid_rowconfigure(0, weight=1)
        current_tree_container.grid_columnconfigure(0, weight=1)

        # 绑定右键菜单事件
        self.current_tree.bind('<Button-3>', self.show_current_tree_context_menu)

        # 绑定鼠标悬停事件显示完整文件名
        self.current_tree.bind('<Motion>', self.show_filename_tooltip)
        self.current_tree.bind('<Leave>', self.hide_filename_tooltip)

        # 预览结构树 - 平衡方案：合理列宽 + 水平滚动条
        preview_tree_container = ttk.Frame(preview_frame)
        preview_tree_container.pack(fill=tk.BOTH, expand=True)

        self.preview_tree = ttk.Treeview(preview_tree_container, height=15)

        # 恢复默认配置：让列宽自动适应，不强制设置宽度
        self.preview_tree.configure(show='tree')
        # 移除固定宽度设置，让Treeview自动适应布局

        # 垂直滚动条
        preview_v_scrollbar = ttk.Scrollbar(preview_tree_container, orient=tk.VERTICAL, command=self.preview_tree.yview)
        self.preview_tree.configure(yscrollcommand=preview_v_scrollbar.set)

        # 水平滚动条
        preview_h_scrollbar = ttk.Scrollbar(preview_tree_container, orient=tk.HORIZONTAL, command=self.preview_tree.xview)
        self.preview_tree.configure(xscrollcommand=preview_h_scrollbar.set)

        # 使用grid布局
        self.preview_tree.grid(row=0, column=0, sticky='nsew')
        preview_v_scrollbar.grid(row=0, column=1, sticky='ns')
        preview_h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置权重
        preview_tree_container.grid_rowconfigure(0, weight=1)
        preview_tree_container.grid_columnconfigure(0, weight=1)

        # 填充当前结构
        self.populate_current_tree(operation)

        # 填充预览结构
        self.populate_preview_tree(operation)

        # 绑定拖拽事件
        self.setup_drag_and_drop()

        # 添加刷新按钮
        refresh_frame = ttk.Frame(parent)
        refresh_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(refresh_frame, text="🔄 刷新",
                  command=lambda: self.refresh_folder_structure()).pack(side=tk.LEFT)

        ttk.Label(refresh_frame, text="💡 提示：刷新会重新扫描源文件夹目录结构，预览也会随之更新",
                 foreground='blue', font=('Arial', 9)).pack(side=tk.RIGHT)

    def populate_current_tree(self, operation):
        """填充当前目录结构"""
        self.current_tree.delete(*self.current_tree.get_children())

        folder_name = operation['folder_name']

        # 统计各类文件数量
        video_count = len(operation['video_files'])
        image_folder_count = len(operation['image_folders'])
        # 统计图片文件夹中的图片总数
        image_count = sum(folder['image_count'] for folder in operation['image_folders'])
        # 加上单独的图片文件数量
        image_file_count = len(operation.get('image_files', []))
        total_image_count = image_count + image_file_count
        other_count = len(operation['other_files'])
        small_count = len(operation['small_files'])

        # 生成文件夹标注
        annotations = []
        if video_count > 0:
            annotations.append(f"📹{video_count}")
        if total_image_count > 0:
            if image_folder_count > 0 and image_file_count > 0:
                # 既有图片文件夹又有单独图片文件
                annotations.append(f"📂{image_folder_count}({image_count}张)")
                annotations.append(f"🖼️{image_file_count}")
            elif image_folder_count > 0:
                # 只有图片文件夹
                annotations.append(f"📂{image_folder_count}({image_count}张)")
            else:
                # 只有单独图片文件
                annotations.append(f"🖼️{image_file_count}")
        if other_count > 0:
            annotations.append(f"📄{other_count}")
        if small_count > 0:
            annotations.append(f"🗑️{small_count}")

        annotation_text = f" [{', '.join(annotations)}]" if annotations else ""
        root_item = self.current_tree.insert('', 'end', text=f"📂 {folder_name}{annotation_text}", open=True)

        # 按实际文件夹结构显示
        self.add_folder_structure_to_tree(root_item, operation)

    def add_folder_structure_to_tree(self, parent_item, operation):
        """按实际文件夹结构添加到树中"""
        folder_path = operation['folder_path']

        # 构建层级文件夹结构
        folder_tree = {}

        # 收集所有文件和文件夹的信息
        all_items = []

        # 处理视频文件
        for video_file in operation['video_files']:
            file_path = video_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'video',
                'path': relative_path,
                'data': video_file
            })

        # 处理图片文件夹
        for image_folder in operation['image_folders']:
            folder_path_img = image_folder['folder_path']
            relative_path = os.path.relpath(folder_path_img, folder_path)
            all_items.append({
                'type': 'folder',
                'subtype': 'image_folder',
                'path': relative_path,
                'data': image_folder
            })

        # 处理单独的图片文件
        for image_file in operation.get('image_files', []):
            file_path = image_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'image_file',
                'path': relative_path,
                'data': image_file
            })

        # 处理其他文件
        for other_file in operation['other_files']:
            file_path = other_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'other',
                'path': relative_path,
                'data': other_file
            })

        # 处理小文件
        for small_file in operation['small_files']:
            file_path = small_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'small',
                'path': relative_path,
                'data': small_file
            })

        # 构建层级结构
        self.build_folder_tree(folder_tree, all_items)

        # 渲染树结构
        self.render_folder_tree(parent_item, folder_tree, '')

    def build_folder_tree(self, folder_tree, all_items):
        """构建层级文件夹结构"""
        for item in all_items:
            # 跳过当前目录路径（.）
            if item['path'] == '.' or item['path'] == '':
                continue

            path_parts = item['path'].split(os.sep)

            # 如果是根目录下的文件或文件夹
            if len(path_parts) == 1:
                if 'items' not in folder_tree:
                    folder_tree['items'] = []
                folder_tree['items'].append(item)
            else:
                # 多级路径，需要构建层级结构
                current_level = folder_tree

                # 遍历路径的每一级（除了最后一级）
                for part in path_parts[:-1]:
                    if 'folders' not in current_level:
                        current_level['folders'] = {}

                    if part not in current_level['folders']:
                        current_level['folders'][part] = {}

                    current_level = current_level['folders'][part]

                # 添加最后一级（文件或文件夹）
                if 'items' not in current_level:
                    current_level['items'] = []
                current_level['items'].append(item)

    def render_folder_tree(self, parent_item, folder_tree, current_path):
        """渲染文件夹树结构"""
        # 收集已经作为文件夹处理的路径名称，避免重复显示
        processed_folder_names = set()

        # 先添加当前级别的子文件夹（通过路径分析得到的普通文件夹）
        if 'folders' in folder_tree:
            for folder_name, sub_tree in folder_tree['folders'].items():
                processed_folder_names.add(folder_name)  # 记录已处理的文件夹名称

                # 统计子文件夹内容
                stats = self.calculate_folder_stats(sub_tree)

                # 生成标注
                annotations = []
                if stats['video_count'] > 0:
                    annotations.append(f"📹{stats['video_count']}")

                # 智能显示图片信息
                total_image_count = stats['image_count'] + stats['image_file_count']
                if total_image_count > 0:
                    if stats['image_folder_count'] > 0 and stats['image_file_count'] > 0:
                        # 既有图片文件夹又有单独图片文件
                        annotations.append(f"📂{stats['image_folder_count']}({stats['image_count']}张)")
                        annotations.append(f"🖼️{stats['image_file_count']}")
                    elif stats['image_folder_count'] > 0:
                        # 只有图片文件夹
                        annotations.append(f"📂{stats['image_folder_count']}({stats['image_count']}张)")
                    else:
                        # 只有单独图片文件
                        annotations.append(f"🖼️{stats['image_file_count']}")

                if stats['other_count'] > 0:
                    annotations.append(f"📄{stats['other_count']}")
                if stats['small_count'] > 0:
                    annotations.append(f"🗑️{stats['small_count']}")

                annotation_text = f" [{', '.join(annotations)}]" if annotations else ""
                folder_node = self.current_tree.insert(parent_item, 'end',
                                                      text=f"📂 {folder_name}{annotation_text}", open=False)

                # 递归渲染子文件夹
                sub_path = os.path.join(current_path, folder_name) if current_path else folder_name
                self.render_folder_tree(folder_node, sub_tree, sub_path)

        # 然后添加当前级别的文件和叶子节点的图片文件夹（但要避免重复）
        if 'items' in folder_tree:
            items_to_show = folder_tree['items']  # 显示所有项目

            for item in items_to_show:
                if item['type'] == 'file':
                    # 处理普通文件
                    file_name = item['data']['name']
                    file_size = self.format_file_size(item['data']['size'])

                    if item['subtype'] == 'video':
                        self.current_tree.insert(parent_item, 'end', text=f"🎬 {file_name} ({file_size})")
                    elif item['subtype'] == 'image_file':  # 单独的图片文件
                        self.current_tree.insert(parent_item, 'end', text=f"🖼️ {file_name} ({file_size})")
                    elif item['subtype'] == 'other':
                        self.current_tree.insert(parent_item, 'end', text=f"📄 {file_name} ({file_size})")
                    elif item['subtype'] == 'small':
                        self.current_tree.insert(parent_item, 'end', text=f"🗑️ {file_name} ({file_size})")

                elif item['type'] == 'folder' and item['subtype'] == 'image_folder':
                    # 处理图片文件夹，但要避免重复显示已经作为普通文件夹处理过的
                    folder_name = os.path.basename(item['path'])

                    # 如果这个文件夹名称已经作为普通文件夹处理过，就跳过
                    if folder_name in processed_folder_names:
                        continue

                    image_count = item['data']['image_count']
                    img_folder_node = self.current_tree.insert(parent_item, 'end',
                                                             text=f"📂 {folder_name} [🖼️{image_count}]", open=False)

                    # 显示部分图片
                    for img_file in item['data']['image_files'][:5]:
                        img_name = img_file['name']
                        img_size = self.format_file_size(img_file['size'])
                        self.current_tree.insert(img_folder_node, 'end', text=f"🖼️ {img_name} ({img_size})")

                    if len(item['data']['image_files']) > 5:
                        self.current_tree.insert(img_folder_node, 'end',
                                               text=f"... 还有 {len(item['data']['image_files']) - 5} 张图片")



    def calculate_folder_stats(self, folder_tree):
        """计算文件夹统计信息"""
        stats = {
            'video_count': 0,
            'image_folder_count': 0,  # 图片文件夹数量
            'image_count': 0,  # 图片文件夹中的图片总数
            'image_file_count': 0,  # 单独的图片文件数量
            'other_count': 0,
            'small_count': 0
        }

        # 统计当前级别的项目
        if 'items' in folder_tree:
            for item in folder_tree['items']:
                if item['type'] == 'file':
                    if item['subtype'] == 'video':
                        stats['video_count'] += 1
                    elif item['subtype'] == 'image_file':  # 单独的图片文件
                        stats['image_file_count'] += 1
                    elif item['subtype'] == 'other':
                        stats['other_count'] += 1
                    elif item['subtype'] == 'small':
                        stats['small_count'] += 1
                elif item['type'] == 'folder' and item['subtype'] == 'image_folder':
                    # 统计图片文件夹数量和其中的图片数量
                    stats['image_folder_count'] += 1
                    try:
                        image_count = item['data']['image_count']
                        stats['image_count'] += image_count
                    except KeyError:
                        # 如果没有image_count字段，尝试从image_files计算
                        if 'image_files' in item['data']:
                            image_count = len(item['data']['image_files'])
                            stats['image_count'] += image_count

        # 递归统计子文件夹
        if 'folders' in folder_tree:
            for sub_tree in folder_tree['folders'].values():
                sub_stats = self.calculate_folder_stats(sub_tree)
                stats['video_count'] += sub_stats['video_count']
                stats['image_folder_count'] += sub_stats['image_folder_count']
                stats['image_count'] += sub_stats['image_count']
                stats['image_file_count'] += sub_stats['image_file_count']
                stats['other_count'] += sub_stats['other_count']
                stats['small_count'] += sub_stats['small_count']

        return stats

    def populate_preview_tree(self, operation):
        """填充预览目录结构"""
        self.preview_tree.delete(*self.preview_tree.get_children())

        folder_name = operation['folder_name']
        user_actions = operation['user_actions']

        # 统计整理后的文件数量
        video_count = len(operation['video_files'])
        image_count = len(operation['image_folders'])
        other_count = len(operation['other_files'])
        small_count = len(operation['small_files'])

        # 根据用户设置计算整理后的分布
        preview_annotations = []
        if video_count > 0:
            preview_annotations.append(f"📹{video_count}")
        if image_count > 0:
            preview_annotations.append(f"🖼️{image_count}")
        if other_count > 0:
            preview_annotations.append(f"📄{other_count}")
        if small_count > 0 and user_actions.get('small_action', 'delete') != 'delete':
            preview_annotations.append(f"🗑️{small_count}")

        preview_annotation_text = f" [{', '.join(preview_annotations)}]" if preview_annotations else ""
        root_item = self.preview_tree.insert('', 'end', text=f"📁 {folder_name}{preview_annotation_text}", open=True)

        # 创建预览结构
        self.create_preview_structure(root_item, operation, user_actions)

    def create_preview_structure(self, root_item, operation, user_actions):
        """创建预览目录结构"""
        # 如果所有操作都是保留在原位置，则显示原始结构
        all_keep = (
            user_actions.get('video_action', 'auto') == 'keep' and
            user_actions.get('image_action', 'auto') == 'keep' and
            user_actions.get('other_action', 'auto') == 'keep' and
            user_actions.get('small_action', 'delete') in ['keep', 'delete']
        )

        if all_keep:
            # 显示原始结构（但标记删除的小文件）
            self.create_preview_original_structure(root_item, operation, user_actions)
        else:
            # 显示重新整理后的结构
            self.create_preview_reorganized_structure(root_item, operation, user_actions)

    def create_preview_original_structure(self, root_item, operation, user_actions):
        """创建保持原始结构的预览"""
        # 构建原始结构但标记操作
        folder_path = operation['folder_path']

        # 收集所有文件和文件夹的信息，并标记操作
        all_items = []

        # 处理视频文件
        for video_file in operation['video_files']:
            file_path = video_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'video',
                'path': relative_path,
                'data': video_file,
                'action': user_actions.get('video_action', 'auto')
            })

        # 处理图片文件夹
        for image_folder in operation['image_folders']:
            folder_path_img = image_folder['folder_path']
            relative_path = os.path.relpath(folder_path_img, folder_path)
            all_items.append({
                'type': 'folder',
                'subtype': 'image_folder',
                'path': relative_path,
                'data': image_folder,
                'action': user_actions.get('image_action', 'auto')
            })

        # 处理单独的图片文件
        for image_file in operation.get('image_files', []):
            file_path = image_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'image_file',
                'path': relative_path,
                'data': image_file,
                'action': user_actions.get('image_file_action', 'auto')
            })

        # 处理其他文件
        for other_file in operation['other_files']:
            file_path = other_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'other',
                'path': relative_path,
                'data': other_file,
                'action': user_actions.get('other_action', 'auto')
            })

        # 处理小文件
        for small_file in operation['small_files']:
            file_path = small_file['path']
            relative_path = os.path.relpath(file_path, folder_path)
            all_items.append({
                'type': 'file',
                'subtype': 'small',
                'path': relative_path,
                'data': small_file,
                'action': user_actions.get('small_action', 'delete')
            })

        # 构建层级结构
        preview_tree = {}
        self.build_folder_tree(preview_tree, all_items)

        # 渲染预览树结构
        self.render_preview_tree(root_item, preview_tree, '', user_actions)

    def create_preview_reorganized_structure(self, root_item, operation, user_actions):
        """创建重新整理后的预览结构"""
        # 收集需要创建的分类文件夹
        category_folders = {}
        keep_in_place = {}  # 保持原位置的文件

        # 处理视频文件
        if operation['video_files']:
            video_action = user_actions.get('video_action', 'auto')
            target_folder = self.get_preview_target_folder(video_action, operation)

            if target_folder == 'keep_in_place':
                # 保持在原位置
                for video_file in operation['video_files']:
                    file_path = video_file['path']
                    relative_path = os.path.relpath(file_path, operation['folder_path'])
                    if 'videos' not in keep_in_place:
                        keep_in_place['videos'] = []
                    keep_in_place['videos'].append({
                        'path': relative_path,
                        'data': video_file
                    })
            elif target_folder == 'root':
                # 移动到根目录
                if 'root_videos' not in keep_in_place:
                    keep_in_place['root_videos'] = []
                for video_file in operation['video_files']:
                    keep_in_place['root_videos'].append({
                        'path': os.path.basename(video_file['path']),  # 只保留文件名
                        'data': video_file
                    })
            else:
                # 添加到分类文件夹
                if target_folder == 'auto_classify':
                    # 默认分类：视频文件放到视频文件夹
                    target_folder = '视频'

                if target_folder not in category_folders:
                    category_folders[target_folder] = {'videos': [], 'images': [], 'others': [], 'smalls': []}
                category_folders[target_folder]['videos'] = operation['video_files']

        # 处理图片文件夹
        if operation['image_folders']:
            image_action = user_actions.get('image_action', 'auto')
            if image_action == 'skip':
                # 跳过图片文件夹处理
                pass
            else:
                target_folder = self.get_preview_target_folder(image_action, operation)

                if target_folder == 'root':
                    # 直接在根目录显示图片文件夹
                    for image_folder in operation['image_folders'][:8]:
                        folder_path = image_folder['folder_path']
                        folder_name_only = os.path.basename(folder_path)
                        image_count = image_folder['image_count']
                        img_node = self.preview_tree.insert(root_item, 'end', text=f"📂 {folder_name_only} [🖼️{image_count}]", open=False)
                        # 显示部分图片
                        for img_file in image_folder['image_files'][:3]:
                            img_name = img_file['name']
                            img_size = self.format_file_size(img_file['size'])
                            self.preview_tree.insert(img_node, 'end', text=f"🖼️ {img_name} ({img_size})")
                    if len(operation['image_folders']) > 8:
                        self.preview_tree.insert(root_item, 'end', text=f"... 还有 {len(operation['image_folders']) - 8} 个图片文件夹")
                else:
                    # 添加到分类文件夹
                    if target_folder == 'auto_classify':
                        # 默认分类：图片文件夹放到图片文件夹
                        target_folder = '图片'

                    if target_folder not in category_folders:
                        category_folders[target_folder] = {'videos': [], 'images': [], 'others': [], 'smalls': []}
                    category_folders[target_folder]['images'] = operation['image_folders']

        # 处理单独图片文件
        if operation.get('image_files', []):
            image_file_action = user_actions.get('image_file_action', 'auto')
            if image_file_action == 'skip':
                # 跳过图片文件处理
                pass
            else:
                target_folder = self.get_preview_target_folder(image_file_action, operation)

                if target_folder == 'keep_in_place':
                    # 保持在原位置
                    for image_file in operation['image_files']:
                        file_path = image_file['path']
                        relative_path = os.path.relpath(file_path, operation['folder_path'])
                        if 'image_files' not in keep_in_place:
                            keep_in_place['image_files'] = []
                        keep_in_place['image_files'].append({
                            'path': relative_path,
                            'data': image_file
                        })
                elif target_folder == 'root':
                    # 移动到根目录
                    if 'root_image_files' not in keep_in_place:
                        keep_in_place['root_image_files'] = []
                    for image_file in operation['image_files']:
                        keep_in_place['root_image_files'].append({
                            'path': os.path.basename(image_file['path']),  # 只保留文件名
                            'data': image_file
                        })
                else:
                    # 添加到分类文件夹
                    if target_folder == 'auto_classify':
                        # 默认分类：图片文件放到图片文件夹
                        target_folder = '图片'

                    if target_folder not in category_folders:
                        category_folders[target_folder] = {'videos': [], 'images': [], 'others': [], 'smalls': [], 'image_files': []}
                    if 'image_files' not in category_folders[target_folder]:
                        category_folders[target_folder]['image_files'] = []
                    category_folders[target_folder]['image_files'] = operation['image_files']

        # 处理其他文件
        if operation['other_files']:
            other_action = user_actions.get('other_action', 'auto')
            target_folder = self.get_preview_target_folder(other_action, operation)

            if target_folder == 'delete':
                # 删除其他文件 - 不在预览中显示
                pass  # 删除的文件不需要在预览中显示
            elif target_folder == 'keep_in_place':
                # 保持在原位置
                for other_file in operation['other_files']:
                    file_path = other_file['path']
                    relative_path = os.path.relpath(file_path, operation['folder_path'])
                    if 'others' not in keep_in_place:
                        keep_in_place['others'] = []
                    keep_in_place['others'].append({
                        'path': relative_path,
                        'data': other_file
                    })
            elif target_folder == 'root':
                # 移动到根目录
                if 'root_others' not in keep_in_place:
                    keep_in_place['root_others'] = []
                for other_file in operation['other_files']:
                    keep_in_place['root_others'].append({
                        'path': os.path.basename(other_file['path']),  # 只保留文件名
                        'data': other_file
                    })
            else:
                # 添加到分类文件夹
                if target_folder == 'auto_classify':
                    # 默认分类：其他文件放到其他文件夹
                    target_folder = '其他'

                if target_folder not in category_folders:
                    category_folders[target_folder] = {'videos': [], 'images': [], 'others': [], 'smalls': []}
                category_folders[target_folder]['others'] = operation['other_files']

        # 处理小文件
        small_action = user_actions.get('small_action', 'delete')
        if operation['small_files']:
            if small_action == 'delete':
                # 统计0字节文件和小文件
                zero_byte_files = [f for f in operation['small_files'] if f['size'] == 0]
                small_size_files = [f for f in operation['small_files'] if f['size'] > 0]

                # 生成删除节点的标题
                delete_title = "🗑️ 将删除 ("
                if zero_byte_files and small_size_files:
                    delete_title += f"{len(zero_byte_files)}个0字节文件, {len(small_size_files)}个小文件"
                elif zero_byte_files:
                    delete_title += f"{len(zero_byte_files)}个0字节文件"
                elif small_size_files:
                    delete_title += f"{len(small_size_files)}个小文件"
                delete_title += ")"

                deleted_node = self.preview_tree.insert(root_item, 'end', text=delete_title, open=False)
                for small_file in operation['small_files'][:3]:
                    file_name = small_file['name']
                    if small_file['size'] == 0:
                        self.preview_tree.insert(deleted_node, 'end', text=f"🗑️ {file_name} (0字节)")
                    else:
                        size_str = self.format_file_size(small_file['size'])
                        self.preview_tree.insert(deleted_node, 'end', text=f"🗑️ {file_name} ({size_str})")
                if len(operation['small_files']) > 3:
                    self.preview_tree.insert(deleted_node, 'end', text=f"... 还有 {len(operation['small_files']) - 3} 个")
            elif small_action == 'keep':
                # 保持在根目录
                for small_file in operation['small_files'][:5]:
                    file_name = small_file['name']
                    size_str = self.format_file_size(small_file['size'])
                    self.preview_tree.insert(root_item, 'end', text=f"� {file_name} ({size_str})")
                if len(operation['small_files']) > 5:
                    self.preview_tree.insert(root_item, 'end', text=f"... 还有 {len(operation['small_files']) - 5} 个小文件")
            else:  # move_to_other
                if '其他' not in category_folders:
                    category_folders['其他'] = {'videos': [], 'images': [], 'others': [], 'smalls': []}
                category_folders['其他']['smalls'] = operation['small_files']

        # 创建分类文件夹
        for folder_name, contents in category_folders.items():
            # 统计分类文件夹中的内容
            folder_annotations = []
            if contents['videos']:
                folder_annotations.append(f"📹{len(contents['videos'])}")
            # 分别显示图片文件夹和图片文件
            if contents['images']:
                # 显示图片文件夹数量和其中包含的图片总数
                total_images_in_folders = sum(img_folder['image_count'] for img_folder in contents['images'])
                folder_count = len(contents['images'])
                folder_annotations.append(f"📂{folder_count}({total_images_in_folders}张)")
            if contents.get('image_files', []):
                # 显示单独的图片文件数量
                folder_annotations.append(f"🖼️{len(contents['image_files'])}")
            if contents['others']:
                folder_annotations.append(f"📄{len(contents['others'])}")
            if contents['smalls']:
                folder_annotations.append(f"🗑️{len(contents['smalls'])}")

            folder_annotation_text = f" [{', '.join(folder_annotations)}]" if folder_annotations else ""
            folder_node = self.preview_tree.insert(root_item, 'end', text=f"📂 {folder_name}{folder_annotation_text}", open=True)

            # 添加视频文件
            for video_file in contents['videos'][:5]:
                file_name = video_file['name']
                size_str = self.format_file_size(video_file['size'])
                self.preview_tree.insert(folder_node, 'end', text=f"🎬 {file_name} ({size_str})")
            if len(contents['videos']) > 5:
                self.preview_tree.insert(folder_node, 'end', text=f"... 还有 {len(contents['videos']) - 5} 个视频文件")

            # 添加图片文件夹
            for image_folder in contents['images'][:5]:
                folder_path = image_folder['folder_path']
                folder_name_only = os.path.basename(folder_path)
                image_count = image_folder['image_count']
                img_node = self.preview_tree.insert(folder_node, 'end', text=f"📂 {folder_name_only} [🖼️{image_count}]", open=False)
                # 显示部分图片
                for img_file in image_folder['image_files'][:3]:
                    img_name = img_file['name']
                    img_size = self.format_file_size(img_file['size'])
                    self.preview_tree.insert(img_node, 'end', text=f"🖼️ {img_name} ({img_size})")
            if len(contents['images']) > 5:
                self.preview_tree.insert(folder_node, 'end', text=f"... 还有 {len(contents['images']) - 5} 个图片文件夹")

            # 添加单独图片文件
            if 'image_files' in contents:
                for image_file in contents['image_files'][:5]:
                    file_name = image_file['name']
                    size_str = self.format_file_size(image_file['size'])
                    self.preview_tree.insert(folder_node, 'end', text=f"🖼️ {file_name} ({size_str})")
                if len(contents['image_files']) > 5:
                    self.preview_tree.insert(folder_node, 'end', text=f"... 还有 {len(contents['image_files']) - 5} 个图片文件")

            # 添加其他文件
            for other_file in contents['others'][:5]:
                file_name = other_file['name']
                size_str = self.format_file_size(other_file['size'])
                self.preview_tree.insert(folder_node, 'end', text=f"📄 {file_name} ({size_str})")
            if len(contents['others']) > 5:
                self.preview_tree.insert(folder_node, 'end', text=f"... 还有 {len(contents['others']) - 5} 个其他文件")

            # 添加小文件
            for small_file in contents['smalls'][:3]:
                file_name = small_file['name']
                size_str = self.format_file_size(small_file['size'])
                self.preview_tree.insert(folder_node, 'end', text=f"📄 {file_name} ({size_str})")
            if len(contents['smalls']) > 3:
                self.preview_tree.insert(folder_node, 'end', text=f"... 还有 {len(contents['smalls']) - 3} 个小文件")

        # 处理移动到根目录的文件（直接显示在根目录下）
        if keep_in_place:
            # 先显示移动到根目录的文件
            for file_type, files in keep_in_place.items():
                if file_type.startswith('root_'):
                    for file_info in files[:5]:  # 限制显示数量
                        if file_type == 'root_videos':
                            file_name = file_info['data']['name']
                            size_str = self.format_file_size(file_info['data']['size'])
                            self.preview_tree.insert(root_item, 'end', text=f"🎬 {file_name} ({size_str}) [从子文件夹移动]")
                        elif file_type == 'root_others':
                            file_name = file_info['data']['name']
                            size_str = self.format_file_size(file_info['data']['size'])
                            self.preview_tree.insert(root_item, 'end', text=f"📄 {file_name} ({size_str}) [从子文件夹移动]")
                        elif file_type == 'root_image_files':
                            file_name = file_info['data']['name']
                            size_str = self.format_file_size(file_info['data']['size'])
                            self.preview_tree.insert(root_item, 'end', text=f"🖼️ {file_name} ({size_str}) [从子文件夹移动]")

                    # 如果文件太多，显示省略信息
                    if len(files) > 5:
                        if file_type == 'root_videos':
                            file_type_name = "视频文件"
                        elif file_type == 'root_image_files':
                            file_type_name = "图片文件"
                        else:
                            file_type_name = "其他文件"
                        self.preview_tree.insert(root_item, 'end', text=f"... 还有 {len(files) - 5} 个{file_type_name}从子文件夹移动到根目录")

            # 构建保持原位置文件的层级结构
            keep_tree = {}
            all_keep_items = []

            # 收集所有保持原位置的文件（不包括移动到根目录的）
            for file_type, files in keep_in_place.items():
                if not file_type.startswith('root_'):  # 跳过已经处理的根目录文件
                    for file_info in files:
                        if file_type == 'videos':
                            all_keep_items.append({
                                'type': 'file',
                                'subtype': 'video',
                                'path': file_info['path'],
                                'data': file_info['data'],
                                'action': 'keep'
                            })
                        elif file_type == 'others':
                            all_keep_items.append({
                                'type': 'file',
                                'subtype': 'other',
                                'path': file_info['path'],
                                'data': file_info['data'],
                                'action': 'keep'
                            })
                        elif file_type == 'image_files':
                            all_keep_items.append({
                                'type': 'file',
                                'subtype': 'image_file',
                                'path': file_info['path'],
                                'data': file_info['data'],
                                'action': 'keep'
                            })

            # 如果有保持原位置的文件，构建并渲染层级结构
            if all_keep_items:
                self.build_folder_tree(keep_tree, all_keep_items)
                self.render_preview_tree(root_item, keep_tree, '', user_actions)

    def get_preview_target_folder(self, action, operation):
        """获取预览目标文件夹，与实际操作逻辑保持完全一致"""
        if action == 'skip':
            return 'skip'  # 跳过处理
        elif action == 'keep':
            return 'keep_in_place'  # 保持在原位置
        elif action == 'move_to_root':
            return 'root'  # 移动到根目录（父文件夹）
        elif action == 'move_to_video':
            return '视频'
        elif action == 'move_to_local_video':
            return '视频'  # 在预览中显示为视频文件夹
        elif action == 'move_to_image':
            return '图片'
        elif action == 'move_to_local_image':
            return '图片'  # 在预览中显示为图片文件夹
        elif action == 'move_to_other':
            return '其他'
        elif action == 'delete':
            return 'delete'  # 删除文件
        else:  # auto
            # 使用默认判断逻辑
            file_types_count = 0
            if operation['video_files']:
                file_types_count += 1
            if operation['image_folders']:
                file_types_count += 1
            if operation['other_files']:
                file_types_count += 1

            if file_types_count <= 1:
                # 只有一种文件类型，移动到根目录（父文件夹）
                return 'root'
            else:
                # 多种文件类型，创建对应的分类文件夹
                return 'auto_classify'

    def will_create_folder(self, user_actions, folder_type):
        """判断是否会创建指定类型的文件夹"""
        for _, action_value in user_actions.items():
            if action_value == f'move_to_{folder_type}':
                return True
        return False

    def get_target_location_name(self, action, will_create_folder):
        """获取目标位置名称"""
        if action == 'keep':
            return "当前位置"  # 这个值会被特殊处理
        elif action == 'move_to_video':
            return "视频"
        elif action == 'move_to_image':
            return "图片"
        elif action == 'move_to_other':
            return "其他"
        else:  # auto
            if will_create_folder:
                return "默认分类"
            else:
                return "当前位置"

    def refresh_preview_tree(self, operation):
        """刷新预览树"""
        self.populate_preview_tree(operation)

    def refresh_folder_structure(self):
        """刷新源文件夹目录结构，重新扫描并更新预览"""
        try:
            # 检查是否选择了文件夹
            if not self.selected_folder.get():
                messagebox.showwarning("警告", "请先选择要整理的文件夹")
                return

            # 检查文件夹是否存在
            root_path = self.selected_folder.get()
            if not os.path.exists(root_path):
                messagebox.showerror("错误", f"文件夹不存在: {root_path}")
                return

            # 保存当前的文件夹索引和文件夹名称
            current_index = getattr(self, 'current_preview_index', 0)
            current_folder_name = None
            if hasattr(self, 'preview_operations') and self.preview_operations and current_index < len(self.preview_operations):
                current_folder_name = self.preview_operations[current_index]['folder_name']

            # 显示刷新状态
            self.status_var.set("正在刷新目录结构...")
            # self.log_message("🔄 开始刷新源文件夹目录结构...")

            # 重新生成预览数据
            self.generate_preview_data()

            # 尝试恢复到之前的文件夹位置
            if current_folder_name and hasattr(self, 'preview_operations') and self.preview_operations:
                # 查找相同名称的文件夹
                for i, operation in enumerate(self.preview_operations):
                    if operation['folder_name'] == current_folder_name:
                        self.current_preview_index = i
                        # 如果预览窗口已打开，重新创建预览窗口
                        if hasattr(self, 'preview_window') and self.preview_window:
                            self.create_single_folder_preview()
                        self.log_message(f"✅ 已恢复到文件夹: {current_folder_name}")
                        break
                else:
                    # 如果找不到相同名称的文件夹，尝试保持相同的索引位置
                    if current_index < len(self.preview_operations):
                        self.current_preview_index = current_index
                        # 如果预览窗口已打开，重新创建预览窗口
                        if hasattr(self, 'preview_window') and self.preview_window:
                            self.create_single_folder_preview()
                        self.log_message(f"✅ 已恢复到索引位置: {current_index}")
                    else:
                        # 如果索引超出范围，显示第一个文件夹
                        self.current_preview_index = 0
                        # 如果预览窗口已打开，重新创建预览窗口
                        if hasattr(self, 'preview_window') and self.preview_window:
                            self.create_single_folder_preview()
                        self.log_message("✅ 已显示第一个文件夹")

            # self.log_message("✅ 目录结构刷新完成")
            self.status_var.set("目录结构已刷新")

        except Exception as e:
            self.log_message(f"❌ 刷新目录结构时出错: {str(e)}")
            self.status_var.set("刷新失败")
            messagebox.showerror("错误", f"刷新目录结构时出错:\n{str(e)}")

    def validate_preview_consistency(self, operation):
        """验证预览与实际操作的一致性"""
        folder_name = operation['folder_name']
        user_actions = operation['user_actions']

        # 验证每种文件类型的处理方式
        consistency_issues = []

        # 检查视频文件处理
        if operation['video_files']:
            video_action = user_actions.get('video_action', 'auto')
            preview_target = self.get_preview_target_folder(video_action, operation)
            actual_target = self.get_actual_target_folder(video_action, operation, 'video')

            if preview_target != actual_target:
                consistency_issues.append(f"视频文件: 预览目标({preview_target}) != 实际目标({actual_target})")

        # 检查图片文件夹处理
        if operation['image_folders']:
            image_action = user_actions.get('image_action', 'auto')
            preview_target = self.get_preview_target_folder(image_action, operation)
            actual_target = self.get_actual_target_folder(image_action, operation, 'image')

            if preview_target != actual_target:
                consistency_issues.append(f"图片文件夹: 预览目标({preview_target}) != 实际目标({actual_target})")

        # 检查其他文件处理
        if operation['other_files']:
            other_action = user_actions.get('other_action', 'auto')
            preview_target = self.get_preview_target_folder(other_action, operation)
            actual_target = self.get_actual_target_folder(other_action, operation, 'other')

            if preview_target != actual_target:
                consistency_issues.append(f"其他文件: 预览目标({preview_target}) != 实际目标({actual_target})")

        if consistency_issues:
            self.log_message(f"警告：文件夹 '{folder_name}' 存在预览与实际操作不一致的问题:")
            for issue in consistency_issues:
                self.log_message(f"  - {issue}")
            return False
        else:
            self.log_message(f"验证通过：文件夹 '{folder_name}' 预览与实际操作一致")
            return True

    def get_actual_target_folder(self, action, operation, file_type):
        """获取实际操作的目标文件夹（用于一致性验证）"""
        if action == 'keep':
            return 'keep_in_place'
        elif action == 'move_to_root':
            return 'root'
        elif action == 'move_to_video':
            return '视频'
        elif action == 'move_to_image':
            return '图片'
        elif action == 'move_to_other':
            return '其他'
        else:  # auto
            # 使用默认判断逻辑
            file_types_count = 0
            if operation['video_files']:
                file_types_count += 1
            if operation['image_folders']:
                file_types_count += 1
            if operation['other_files']:
                file_types_count += 1

            if file_types_count <= 1:
                return 'root'
            else:
                return 'auto_classify'

    def setup_drag_and_drop(self):
        """设置拖拽功能（简化版本，主要用于演示）"""
        # 绑定双击事件来模拟拖拽效果
        self.current_tree.bind('<Double-1>', self.on_tree_double_click)
        self.preview_tree.bind('<Double-1>', self.on_preview_tree_double_click)

        # 为预览树也添加工具提示
        self.preview_tree.bind('<Motion>', self.show_preview_filename_tooltip)
        self.preview_tree.bind('<Leave>', self.hide_filename_tooltip)

    def on_tree_double_click(self, event):
        """当前树双击事件 - 切换根文件夹"""
        self.log_message("=== 双击事件被触发 ===")

        item = self.current_tree.selection()[0] if self.current_tree.selection() else None
        if not item:
            self.log_message("双击调试: 没有选中项目")
            return

        # 获取项目文本
        item_text = self.current_tree.item(item, 'text')
        self.log_message(f"双击调试: 项目文本='{item_text}'")

        # 检查是否是文件夹项目（以📁或📂开头）
        if not (item_text.startswith('📁') or item_text.startswith('📂')):
            self.log_message(f"双击调试: 不是文件夹项目，跳过")
            return  # 不是文件夹，允许默认行为

        # 尝试从当前树项目中获取文件夹路径
        folder_path = self.get_folder_path_from_current_tree_item(item, item_text)

        # 检查是否是当前根文件夹（避免重复切换）
        current_root = self.selected_folder.get()
        if folder_path == current_root:
            # 如果是根项目，允许展开/折叠，不切换
            parent_item = self.current_tree.parent(item)
            if not parent_item:
                self.log_message("双击根文件夹，允许展开/折叠")
                return  # 允许展开/折叠
            else:
                self.log_message("已经是当前根文件夹")
                return  # 允许展开/折叠

        if folder_path and os.path.exists(folder_path) and os.path.isdir(folder_path):
            # 切换根文件夹
            self.switch_root_folder(folder_path)
            return "break"  # 只在成功切换时阻止默认行为
        else:
            self.log_message(f"无法访问文件夹: {folder_path}")
            return  # 允许展开/折叠

    def get_folder_path_from_current_tree_item(self, item, item_text):
        """从当前树项目获取文件夹路径"""
        try:
            # 清理项目文本，移除图标和注释
            original_text = item_text
            folder_name = item_text.replace('📁 ', '').replace('📂 ', '')

            # 智能移除方括号中的注释（如 [🎬2, 🖼️1]），但保留文件夹名中的方括号
            # 注释的特征：包含emoji符号（🎬📹🖼️📄🗑️等）
            if '[' in folder_name:
                # 查找最后一个方括号，检查是否是注释
                last_bracket_start = folder_name.rfind('[')
                if last_bracket_start != -1:
                    bracket_content = folder_name[last_bracket_start:]
                    # 检查方括号内容是否包含emoji（注释的特征）
                    emoji_pattern = r'[🎬📹🖼️📄🗑️📂,\d\s张个]'
                    import re
                    if re.search(emoji_pattern, bracket_content):
                        # 这是注释，移除它
                        folder_name = folder_name[:last_bracket_start].strip()
                    # 否则保留方括号，因为它是文件夹名的一部分

            self.log_message(f"路径解析调试: 原始文本='{original_text}', 清理后='{folder_name}'")

            # 获取父项目
            parent_item = self.current_tree.parent(item)

            if not parent_item:
                # 这是根项目，应该返回根项目本身代表的文件夹路径
                # 根项目的文件夹名就是当前根文件夹的名称，但路径应该是当前根文件夹本身
                current_root = self.selected_folder.get()
                self.log_message(f"路径解析调试: 根项目，返回当前根目录='{current_root}'")
                return current_root
            else:
                # 这是子项目，需要构建完整路径
                parent_path = self.get_folder_path_from_current_tree_item(parent_item,
                                                                        self.current_tree.item(parent_item, 'text'))
                if parent_path:
                    full_path = os.path.join(parent_path, folder_name)
                    self.log_message(f"路径解析调试: 子项目，父路径='{parent_path}', 文件夹名='{folder_name}', 完整路径='{full_path}'")
                    return full_path

            return None

        except Exception as e:
            self.log_message(f"获取文件夹路径失败: {str(e)}")
            return None

    def on_preview_tree_double_click(self, event):
        """预览树双击事件 - 切换根文件夹"""
        item = self.preview_tree.selection()[0] if self.preview_tree.selection() else None
        if not item:
            return "break"

        # 获取项目文本
        item_text = self.preview_tree.item(item, 'text')

        # 检查是否是文件夹项目（以📁或📂开头）
        if not (item_text.startswith('📁') or item_text.startswith('📂')):
            return "break"

        # 尝试从当前预览操作中找到对应的文件夹路径
        folder_path = self.get_folder_path_from_tree_item(item, item_text)

        # 检查是否是当前根文件夹（避免重复切换）
        current_root = self.selected_folder.get()
        if folder_path == current_root:
            self.log_message("已经是当前根文件夹")
            return "break"

        if folder_path and os.path.exists(folder_path) and os.path.isdir(folder_path):
            # 切换根文件夹
            self.switch_root_folder(folder_path)
        else:
            self.log_message(f"无法访问文件夹: {folder_path}")

        return "break"  # 阻止默认的展开/折叠行为

    def get_folder_path_from_tree_item(self, item, item_text):
        """从树项目获取文件夹路径"""
        try:
            # 清理项目文本，移除图标和注释
            folder_name = item_text.replace('📁 ', '').replace('📂 ', '')
            # 移除方括号中的注释（如 [🎬2, 🖼️1]）
            if '[' in folder_name:
                folder_name = folder_name.split('[')[0].strip()

            # 获取父项目
            parent_item = self.preview_tree.parent(item)

            if not parent_item:
                # 这是根项目，返回当前根文件夹路径
                return self.selected_folder.get()
            else:
                # 这是子项目，需要构建完整路径
                parent_path = self.get_folder_path_from_tree_item(parent_item,
                                                                self.preview_tree.item(parent_item, 'text'))
                if parent_path:
                    return os.path.join(parent_path, folder_name)

            return None

        except Exception as e:
            self.log_message(f"获取文件夹路径失败: {str(e)}")
            return None

    def switch_root_folder(self, new_root_path):
        """切换根文件夹并重新生成预览"""
        try:
            # 更新选择的文件夹
            self.selected_folder.set(new_root_path)
            self.log_message(f"切换根文件夹到: {new_root_path}")

            # 关闭当前预览窗口
            if self.preview_window and self.preview_window.winfo_exists():
                self.preview_window.destroy()
                self.preview_window = None

            # 重新生成预览数据并显示预览窗口
            self.generate_preview_data()

        except Exception as e:
            self.log_message(f"切换根文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"切换根文件夹失败:\n{str(e)}")

    def go_back_to_parent(self):
        """返回上级文件夹"""
        try:
            current_path = self.selected_folder.get()
            parent_path = os.path.dirname(current_path)

            # 检查父目录是否存在且不是根目录
            if parent_path and parent_path != current_path and os.path.exists(parent_path):
                self.switch_root_folder(parent_path)
            else:
                messagebox.showinfo("提示", "已经是最顶级目录了")

        except Exception as e:
            self.log_message(f"返回上级失败: {str(e)}")
            messagebox.showerror("错误", f"返回上级失败:\n{str(e)}")

    def update_back_button_state(self):
        """更新返回按钮的状态"""
        try:
            if hasattr(self, 'back_button'):
                current_path = self.selected_folder.get()
                parent_path = os.path.dirname(current_path)

                # 如果有父目录且父目录存在，则启用返回按钮
                if parent_path and parent_path != current_path and os.path.exists(parent_path):
                    self.back_button.config(state='normal')
                else:
                    self.back_button.config(state='disabled')

        except Exception as e:
            # 忽略按钮状态更新错误
            pass

    def go_back_to_parent_from_main(self):
        """从主界面返回上级文件夹"""
        try:
            current_path = self.selected_folder.get()
            parent_path = os.path.dirname(current_path)

            # 检查父目录是否存在且不是根目录
            if parent_path and parent_path != current_path and os.path.exists(parent_path):
                self.selected_folder.set(parent_path)
                self.log_message(f"返回上级文件夹: {parent_path}")
                # 更新主界面返回按钮状态
                self.update_main_back_button_state()
            else:
                messagebox.showinfo("提示", "已经是最顶级目录了")

        except Exception as e:
            self.log_message(f"返回上级失败: {str(e)}")
            messagebox.showerror("错误", f"返回上级失败:\n{str(e)}")

    def update_main_back_button_state(self):
        """更新主界面返回按钮的状态"""
        try:
            if hasattr(self, 'main_back_button'):
                current_path = self.selected_folder.get()
                if not current_path:
                    self.main_back_button.config(state='disabled')
                    return

                parent_path = os.path.dirname(current_path)

                # 如果有父目录且父目录存在，则启用返回按钮
                if parent_path and parent_path != current_path and os.path.exists(parent_path):
                    self.main_back_button.config(state='normal')
                else:
                    self.main_back_button.config(state='disabled')

        except Exception as e:
            # 忽略按钮状态更新错误
            pass

    def show_current_tree_context_menu(self, event):
        """显示当前目录树的右键菜单（支持多选）"""
        # 获取点击的项目
        clicked_item = self.current_tree.identify_row(event.y)
        if not clicked_item:
            return

        # 如果点击的项目不在当前选择中，则选中它
        current_selection = self.current_tree.selection()
        if clicked_item not in current_selection:
            self.current_tree.selection_set(clicked_item)
            current_selection = [clicked_item]

        # 获取所有选中项目的信息
        selected_items = list(current_selection)
        if not selected_items:
            return

        # 最简单直接的文件文件夹识别
        files = []
        folders = []

        for item in selected_items:
            item_text = self.current_tree.item(item, 'text')

            # 最简单的方法：直接检查是否有子项目
            children = self.current_tree.get_children(item)

            if children:
                # 有子项目 = 文件夹
                folders.append(item)
                # self.log_message(f"识别为文件夹: {item_text} (有{len(children)}个子项)")
            else:
                # 无子项目 = 文件
                files.append(item)
                # self.log_message(f"识别为文件: {item_text} (无子项)")

        # self.log_message(f"识别结果: {len(files)}个文件, {len(folders)}个文件夹")

        # 创建右键菜单
        context_menu = tk.Menu(self.root, tearoff=0)

        # 显示选中项目数量
        total_selected = len(selected_items)
        if total_selected > 1:
            context_menu.add_command(label=f"📊 已选中 {total_selected} 项", state='disabled')
            context_menu.add_separator()

        # 简化的菜单创建逻辑
        if files and not folders:
            # 只选中了文件
            self.create_files_context_menu(context_menu, files)
        elif folders and not files:
            # 只选中了文件夹 - 统一处理所有文件夹
            self.create_folders_context_menu(context_menu, folders)
        elif len(selected_items) == 1:
            # 单选情况
            item = selected_items[0]
            item_text = self.current_tree.item(item, 'text')
            if files:
                self.create_single_file_context_menu(context_menu, item, item_text)
            elif folders:
                # 所有文件夹都使用统一的菜单
                self.create_single_folder_context_menu(context_menu, item, item_text)
        else:
            # 混合选择 - 简化处理
            if files and folders:
                # 文件和文件夹混合选择
                context_menu.add_command(label=f"📊 选中: {len(files)} 个文件, {len(folders)} 个文件夹")
                context_menu.add_separator()
                context_menu.add_command(label=f"🗑️ 删除选中项 ({len(selected_items)} 项)",
                                       command=lambda: self.delete_selected_items(selected_items))
                # 为文件夹添加提升选项
                if folders:
                    self.add_promote_option_to_menu(context_menu, folders)
            else:
                # 其他混合情况，使用文件夹菜单
                self.create_folders_context_menu(context_menu, folders)

        # 显示菜单
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def create_files_context_menu(self, menu, files):
        """创建多个文件的右键菜单"""
        file_count = len(files)

        if file_count == 1:
            # 单个文件 - 调用完整的单文件菜单
            item_text = self.current_tree.item(files[0], 'text')
            self.create_single_file_context_menu(menu, files[0], item_text)
            return
        else:
            # 多个文件
            menu.add_command(label=f"📋 复制 {file_count} 个文件名",
                           command=lambda: self.copy_multiple_names(files))

        menu.add_separator()
        menu.add_command(label=f"⬆️ 向上层移动 ({file_count} 个文件)",
                       command=lambda: self.move_multiple_files_up(files))
        menu.add_command(label=f"📂 移动到其他文件夹 ({file_count} 个文件)",
                       command=lambda: self.move_multiple_files_to_folder(files))
        menu.add_separator()
        menu.add_command(label=f"🗑️ 删除 {file_count} 个文件",
                       command=lambda: self.delete_multiple_files(files))

    def create_folders_context_menu(self, menu, folders):
        """创建多个文件夹的右键菜单"""
        folder_count = len(folders)

        if folder_count == 1:
            # 单个文件夹
            item_text = self.current_tree.item(folders[0], 'text')
            menu.add_command(label="📋 复制文件夹名",
                           command=lambda: self.copy_item_name(item_text))
            menu.add_command(label="📁 在文件管理器中打开",
                           command=lambda: self.open_folder_in_explorer(folders[0]))
            menu.add_separator()
            menu.add_command(label="✏️ 重命名文件夹",
                           command=lambda: self.rename_folder(folders[0]))
        else:
            # 多个文件夹
            menu.add_command(label=f"📋 复制 {folder_count} 个文件夹名",
                           command=lambda: self.copy_multiple_names(folders))

        menu.add_separator()
        menu.add_command(label=f"⬆️ 向上层移动 ({folder_count} 个文件夹)",
                       command=lambda: self.move_multiple_folders_up(folders))
        menu.add_command(label=f"📂 移动到其他位置 ({folder_count} 个文件夹)",
                       command=lambda: self.move_multiple_folders_to_location(folders))

        # 使用通用方法添加提升选项
        self.add_promote_option_to_menu(menu, folders)

        menu.add_separator()
        menu.add_command(label=f"🗑️ 删除 {folder_count} 个文件夹",
                       command=lambda: self.delete_multiple_folders(folders))

    def create_root_folders_context_menu(self, menu, root_folders):
        """创建根文件夹的右键菜单"""
        if len(root_folders) == 1:
            item_text = self.current_tree.item(root_folders[0], 'text')
            menu.add_command(label="📋 复制文件夹名",
                           command=lambda: self.copy_item_name(item_text))
            menu.add_command(label="📁 在文件管理器中打开",
                           command=lambda: self.open_folder_in_explorer(root_folders[0]))

            # 使用通用方法添加提升选项
            self.add_promote_option_to_menu(menu, root_folders[0])

            menu.add_separator()
            menu.add_command(label="✏️ 重命名文件夹",
                           command=lambda: self.rename_folder(root_folders[0]))
            menu.add_command(label="🗑️ 删除文件夹",
                           command=lambda: self.delete_folder(root_folders[0]))
            menu.add_separator()
            menu.add_command(label="🔄 刷新",
                           command=lambda: self.refresh_folder_structure())
        else:
            # 多个根文件夹（通常不会发生，但为了完整性）
            menu.add_command(label="🔄 刷新",
                           command=lambda: self.refresh_folder_structure())

    def create_mixed_selection_context_menu(self, menu, files, folders, root_folders):
        """创建混合选择的右键菜单"""
        total_files = len(files)
        total_folders = len(folders)
        total_root_folders = len(root_folders)

        # 显示选择统计
        selection_info = []
        if total_files > 0:
            selection_info.append(f"{total_files} 个文件")
        if total_folders > 0:
            selection_info.append(f"{total_folders} 个文件夹")
        if total_root_folders > 0:
            selection_info.append(f"{total_root_folders} 个根文件夹")

        menu.add_command(label=f"📊 选中: {', '.join(selection_info)}", state='disabled')
        menu.add_separator()

        # 只提供通用操作
        all_items = files + folders
        if all_items:  # 排除根文件夹，因为根文件夹不能移动
            menu.add_command(label=f"⬆️ 向上层移动 ({len(all_items)} 项)",
                           command=lambda: self.move_multiple_items_up(all_items))
            menu.add_command(label=f"🗑️ 删除选中项 ({len(all_items)} 项)",
                           command=lambda: self.delete_multiple_items(all_items))

        menu.add_separator()
        menu.add_command(label="🔄 刷新",
                       command=lambda: self.refresh_folder_structure())

    def create_single_file_context_menu(self, menu, item, item_text):
        """创建单个文件的右键菜单"""
        menu.add_command(label="📋 复制文件名",
                       command=lambda: self.copy_item_name(item_text))
        menu.add_command(label="📁 在文件管理器中显示",
                       command=lambda: self.show_in_explorer(item))
        menu.add_separator()
        menu.add_command(label="✏️ 按文件夹名重命名",
                       command=lambda: self.rename_file_with_folder_name(item))
        menu.add_separator()
        menu.add_command(label="⬆️ 向上层移动",
                       command=lambda: self.move_file_up(item))
        menu.add_command(label="📂 移动到其他文件夹",
                       command=lambda: self.move_file_to_folder(item))
        menu.add_separator()
        menu.add_command(label="🗑️ 删除文件",
                       command=lambda: self.delete_file(item))

    def create_single_folder_context_menu(self, menu, item, item_text):
        """创建单个文件夹的右键菜单"""
        self.log_message(f"创建普通文件夹右键菜单: {item_text}")

        menu.add_command(label="📋 复制文件夹名",
                       command=lambda: self.copy_item_name(item_text))
        menu.add_command(label="📁 在文件管理器中打开",
                       command=lambda: self.open_folder_in_explorer(item))
        menu.add_separator()
        menu.add_command(label="⬆️ 向上层移动",
                       command=lambda: self.move_folder_up(item))
        menu.add_command(label="📂 移动到其他位置",
                       command=lambda: self.move_folder_to_location(item))

        # 使用通用方法添加提升选项
        self.add_promote_option_to_menu(menu, item)

        menu.add_separator()
        menu.add_command(label="✏️ 重命名文件夹",
                       command=lambda: self.rename_folder(item))
        menu.add_command(label="🗑️ 删除文件夹",
                       command=lambda: self.delete_folder(item))

        self.log_message("普通文件夹菜单创建完成")

    def create_single_root_folder_context_menu(self, menu, item, item_text):
        """创建单个根文件夹的右键菜单"""
        menu.add_command(label="📋 复制文件夹名",
                       command=lambda: self.copy_item_name(item_text))
        menu.add_command(label="📁 在文件管理器中打开",
                       command=lambda: self.open_folder_in_explorer(item))

        # 使用通用方法添加提升选项
        self.add_promote_option_to_menu(menu, item)

        menu.add_separator()
        menu.add_command(label="✏️ 重命名文件夹",
                       command=lambda: self.rename_folder(item))
        menu.add_command(label="🗑️ 删除文件夹",
                       command=lambda: self.delete_folder(item))
        menu.add_separator()
        menu.add_command(label="🔄 刷新",
                       command=lambda: self.refresh_folder_structure())

    def copy_item_name(self, item_text):
        """复制项目名称到剪贴板"""
        try:
            # 提取文件名或文件夹名（去除图标和大小信息）
            import re
            # 匹配文件名：去除图标、大小信息、括号内容等
            name_match = re.search(r'[🎬📄🖼️🗑️📂📁]\s*(.+?)(?:\s*\[.*?\]|\s*\(.*?\))?$', item_text)
            if name_match:
                name = name_match.group(1).strip()
            else:
                name = item_text.strip()

            # 复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append(name)
            self.log_message(f"已复制到剪贴板: {name}")

        except Exception as e:
            self.log_message(f"复制失败: {str(e)}")

    def copy_multiple_names(self, items):
        """复制多个项目名称到剪贴板"""
        try:
            names = []
            for item in items:
                item_text = self.current_tree.item(item, 'text')
                # 提取文件名或文件夹名
                import re
                name_match = re.search(r'[🎬📄🖼️🗑️📂📁]\s*(.+?)(?:\s*\[.*?\]|\s*\(.*?\))?$', item_text)
                if name_match:
                    name = name_match.group(1).strip()
                    names.append(name)

            if names:
                # 将名称用换行符连接
                names_text = '\n'.join(names)

                # 复制到剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(names_text)
                self.log_message(f"已复制 {len(names)} 个项目名称到剪贴板")

        except Exception as e:
            self.log_message(f"复制多个名称失败: {str(e)}")

    def get_item_path(self, item):
        """获取树视图项目的实际文件路径"""
        try:
            # 获取当前操作信息
            if not hasattr(self, 'preview_operations') or not self.preview_operations:
                return None

            current_operation = self.preview_operations[self.current_preview_index]
            folder_path = current_operation['folder_path']

            # 构建路径：从根到当前项目
            path_parts = []
            current_item = item

            while current_item:
                item_text = self.current_tree.item(current_item, 'text')
                # 提取名称（使用改进的方法）
                name = self.extract_clean_name(item_text)
                if name:
                    path_parts.insert(0, name)

                current_item = self.current_tree.parent(current_item)

            # 构建完整路径
            if path_parts:
                # 第一个部分是根文件夹名，需要替换为实际的folder_path
                if len(path_parts) > 1:
                    # 有子路径，构建完整路径
                    full_path = os.path.join(folder_path, *path_parts[1:])
                else:
                    # 只有根文件夹，返回folder_path
                    full_path = folder_path
            else:
                full_path = folder_path

            return full_path

        except Exception as e:
            self.log_message(f"获取路径失败: {str(e)}")
            return None

    def show_in_explorer(self, item):
        """在文件管理器中显示文件"""
        try:
            file_path = self.get_item_path_safe(item)
            if file_path and os.path.exists(file_path):
                import subprocess
                import platform

                system = platform.system()
                if system == "Windows":
                    subprocess.run(['explorer', '/select,', file_path])
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', '-R', file_path])
                else:  # Linux
                    subprocess.run(['xdg-open', os.path.dirname(file_path)])

                self.log_message(f"已在文件管理器中显示: {os.path.basename(file_path)}")
            else:
                messagebox.showerror("错误", "文件不存在或路径无效")

        except Exception as e:
            self.log_message(f"打开文件管理器失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开文件管理器:\n{str(e)}")

    def open_folder_in_explorer(self, item):
        """在文件管理器中打开文件夹"""
        try:
            folder_path = self.get_item_path_safe(item)
            if folder_path and os.path.exists(folder_path):
                import subprocess
                import platform

                system = platform.system()
                if system == "Windows":
                    subprocess.run(['explorer', folder_path])
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', folder_path])
                else:  # Linux
                    subprocess.run(['xdg-open', folder_path])

                self.log_message(f"已打开文件夹: {os.path.basename(folder_path)}")
            else:
                messagebox.showerror("错误", "文件夹不存在或路径无效")

        except Exception as e:
            self.log_message(f"打开文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"无法打开文件夹:\n{str(e)}")

    def move_file_up(self, item):
        """向上层移动文件"""
        try:
            file_path = self.get_item_path_safe(item)
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在或路径无效")
                return

            # 获取文件名和当前目录
            file_name = os.path.basename(file_path)
            current_dir = os.path.dirname(file_path)
            parent_dir = os.path.dirname(current_dir)

            # 检查是否已经在根目录
            if current_dir == parent_dir:
                messagebox.showwarning("警告", "文件已经在根目录，无法继续向上移动")
                return

            # 目标路径
            target_path = os.path.join(parent_dir, file_name)

            # 检查目标是否已存在
            if os.path.exists(target_path):
                result = messagebox.askyesnocancel("文件冲突",
                    f"目标位置已存在文件 '{file_name}'，是否重命名移动？\n\n"
                    f"是：重命名移动\n"
                    f"否：覆盖现有文件\n"
                    f"取消：取消操作")

                if result is None:  # 取消
                    return
                elif result:  # 重命名
                    target_path = self.get_unique_path(target_path)

            # 执行移动
            shutil.move(file_path, target_path)

            # 记录操作
            operation = {
                'type': 'move_file',
                'operation': 'move',
                'source_path': file_path,
                'target_path': target_path,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件已向上移动: {file_name}")

            # 检查源文件夹是否为空，如果为空则询问是否删除
            self.check_and_prompt_delete_empty_folder(current_dir)

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"移动文件失败: {str(e)}")
            messagebox.showerror("错误", f"移动文件失败:\n{str(e)}")

    def move_multiple_files_up(self, files):
        """向上层移动多个文件"""
        try:
            if not files:
                return

            # 确认操作
            result = messagebox.askyesno("确认移动",
                f"确定要将 {len(files)} 个文件向上层移动吗？")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in files:
                try:
                    file_path = self.get_item_path_safe(item)
                    if not file_path:
                        error_count += 1
                        self.log_message(f"无法获取文件路径: {self.current_tree.item(item, 'text')}")
                        continue
                    if not os.path.exists(file_path):
                        error_count += 1
                        self.log_message(f"文件不存在: {file_path}")
                        continue

                    file_name = os.path.basename(file_path)
                    current_dir = os.path.dirname(file_path)
                    parent_dir = os.path.dirname(current_dir)

                    # 检查是否已经在根目录
                    if current_dir == parent_dir:
                        error_count += 1
                        self.log_message(f"文件已在根目录，无法向上移动: {file_name}")
                        continue

                    target_path = os.path.join(parent_dir, file_name)

                    # 处理文件冲突
                    if os.path.exists(target_path):
                        action, new_name, apply_to_all = self.handle_conflict(
                            file_name, "文件", os.path.dirname(target_path))

                        if action == "cancel":
                            return
                        elif action == "skip":
                            continue
                        elif action == "auto_rename":
                            target_path = self.get_unique_path(target_path)
                        elif action == "manual_rename":
                            target_path = os.path.join(parent_dir, new_name)
                            # 如果手动重命名的名称也冲突，自动处理
                            if os.path.exists(target_path):
                                target_path = self.get_unique_path(target_path)
                        elif action == "overwrite":
                            # 覆盖，使用原路径
                            pass

                    # 执行移动
                    shutil.move(file_path, target_path)

                    # 记录操作
                    operation = {
                        'type': 'move_file',
                        'operation': 'move',
                        'source_path': file_path,
                        'target_path': target_path,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1
                    self.log_message(f"移动文件失败: {file_name} - {str(e)}")

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功向上移动 {success_count} 个文件")
            if error_count > 0:
                self.log_message(f"移动失败 {error_count} 个文件")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量移动文件失败: {str(e)}")
            messagebox.showerror("错误", f"批量移动文件失败:\n{str(e)}")

    def move_folder_up(self, item):
        """向上层移动文件夹"""
        try:
            folder_path = self.get_item_path_safe(item)
            if not folder_path or not os.path.exists(folder_path):
                messagebox.showerror("错误", "文件夹不存在或路径无效")
                return

            # 获取文件夹名和当前目录
            folder_name = os.path.basename(folder_path)
            current_dir = os.path.dirname(folder_path)
            parent_dir = os.path.dirname(current_dir)

            # 检查是否已经在根目录
            if current_dir == parent_dir:
                messagebox.showwarning("警告", "文件夹已经在根目录，无法继续向上移动")
                return

            # 目标路径
            target_path = os.path.join(parent_dir, folder_name)

            # 检查目标是否已存在
            if os.path.exists(target_path):
                action, new_name, apply_to_all = self.handle_conflict(
                    folder_name, "文件夹", parent_dir)

                if action == "cancel":
                    return
                elif action == "skip":
                    return
                elif action == "auto_rename":
                    target_path = self.get_unique_path(target_path)
                elif action == "manual_rename":
                    target_path = os.path.join(parent_dir, new_name)
                    # 如果手动重命名的名称也冲突，自动处理
                    if os.path.exists(target_path):
                        target_path = self.get_unique_path(target_path)
                elif action == "merge":
                    # 合并文件夹内容
                    self.merge_folders(folder_path, target_path)
                    return
                elif action == "overwrite":
                    # 删除目标文件夹，然后移动
                    shutil.rmtree(target_path)
                    # 使用原路径

            # 执行移动
            shutil.move(folder_path, target_path)

            # 记录操作
            operation = {
                'type': 'move_folder',
                'operation': 'move',
                'source_path': folder_path,
                'target_path': target_path,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件夹已向上移动: {folder_name}")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"移动文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"移动文件夹失败:\n{str(e)}")

    def move_multiple_folders_up(self, folders):
        """向上层移动多个文件夹"""
        try:
            if not folders:
                return

            # 确认操作
            result = messagebox.askyesno("确认移动",
                f"确定要将 {len(folders)} 个文件夹向上层移动吗？")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in folders:
                try:
                    folder_path = self.get_item_path_safe(item)
                    if not folder_path or not os.path.exists(folder_path):
                        error_count += 1
                        continue

                    folder_name = os.path.basename(folder_path)
                    current_dir = os.path.dirname(folder_path)
                    parent_dir = os.path.dirname(current_dir)

                    # 检查是否已经在根目录
                    if current_dir == parent_dir:
                        error_count += 1
                        continue

                    target_path = os.path.join(parent_dir, folder_name)

                    # 处理文件夹冲突
                    if os.path.exists(target_path):
                        target_path = self.get_unique_path(target_path)

                    # 执行移动
                    shutil.move(folder_path, target_path)

                    # 记录操作
                    operation = {
                        'type': 'move_folder',
                        'operation': 'move',
                        'source_path': folder_path,
                        'target_path': target_path,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功向上移动 {success_count} 个文件夹")
            if error_count > 0:
                self.log_message(f"移动失败 {error_count} 个文件夹")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量移动文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"批量移动文件夹失败:\n{str(e)}")

    def delete_multiple_files(self, files):
        """删除多个文件"""
        try:
            if not files:
                return

            # 统计文件信息
            total_size = 0
            file_names = []

            for item in files:
                file_path = self.get_item_path_safe(item)
                if file_path and os.path.exists(file_path):
                    file_names.append(os.path.basename(file_path))
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        pass

            if not file_names:
                messagebox.showerror("错误", "没有有效的文件可以删除")
                return

            # 确认删除
            file_list = '\n'.join(file_names[:5])  # 最多显示5个文件名
            if len(file_names) > 5:
                file_list += f'\n... 还有 {len(file_names) - 5} 个文件'

            result = messagebox.askyesno("确认删除",
                f"确定要删除 {len(file_names)} 个文件吗？\n\n"
                f"文件列表:\n{file_list}\n\n"
                f"总大小: {self.format_file_size(total_size)}\n\n"
                f"此操作不可撤销！")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in files:
                try:
                    file_path = self.get_item_path_safe(item)
                    if not file_path or not os.path.exists(file_path):
                        error_count += 1
                        continue

                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)

                    # 执行删除
                    os.remove(file_path)

                    # 记录操作
                    operation = {
                        'type': 'delete_file',
                        'operation': 'delete',
                        'source_path': file_path,
                        'name': file_name,
                        'size': file_size,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功删除 {success_count} 个文件")
            if error_count > 0:
                self.log_message(f"删除失败 {error_count} 个文件")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量删除文件失败: {str(e)}")
            messagebox.showerror("错误", f"批量删除文件失败:\n{str(e)}")

    def delete_multiple_folders(self, folders):
        """删除多个文件夹"""
        try:
            if not folders:
                return

            # 统计文件夹信息
            folder_info = []
            total_files = 0
            total_folders = 0
            total_size = 0

            for item in folders:
                folder_path = self.get_item_path(item)
                if folder_path and os.path.exists(folder_path):
                    folder_name = os.path.basename(folder_path)
                    folder_info.append(folder_name)

                    # 统计内容
                    for root, dirs, files in os.walk(folder_path):
                        total_folders += len(dirs)
                        total_files += len(files)
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                total_size += os.path.getsize(file_path)
                            except:
                                pass

            if not folder_info:
                messagebox.showerror("错误", "没有有效的文件夹可以删除")
                return

            # 确认删除
            folder_list = '\n'.join(folder_info[:5])  # 最多显示5个文件夹名
            if len(folder_info) > 5:
                folder_list += f'\n... 还有 {len(folder_info) - 5} 个文件夹'

            result = messagebox.askyesno("确认删除",
                f"确定要删除 {len(folder_info)} 个文件夹及其所有内容吗？\n\n"
                f"文件夹列表:\n{folder_list}\n\n"
                f"总计包含: {total_files} 个文件, {total_folders} 个子文件夹\n"
                f"总大小: {self.format_file_size(total_size)}\n\n"
                f"此操作不可撤销！")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in folders:
                try:
                    folder_path = self.get_item_path(item)
                    if not folder_path or not os.path.exists(folder_path):
                        error_count += 1
                        continue

                    folder_name = os.path.basename(folder_path)

                    # 执行删除
                    shutil.rmtree(folder_path)

                    # 记录操作
                    operation = {
                        'type': 'delete_folder',
                        'operation': 'delete',
                        'source_path': folder_path,
                        'folder_name': folder_name,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功删除 {success_count} 个文件夹")
            if error_count > 0:
                self.log_message(f"删除失败 {error_count} 个文件夹")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量删除文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"批量删除文件夹失败:\n{str(e)}")

    def move_multiple_files_to_folder(self, files):
        """移动多个文件到其他文件夹"""
        try:
            if not files:
                return

            # 选择目标文件夹
            target_folder = filedialog.askdirectory(title="选择目标文件夹")
            if not target_folder:
                return

            success_count = 0
            error_count = 0

            for item in files:
                try:
                    file_path = self.get_item_path(item)
                    if not file_path or not os.path.exists(file_path):
                        error_count += 1
                        continue

                    file_name = os.path.basename(file_path)
                    target_path = os.path.join(target_folder, file_name)

                    # 处理文件冲突
                    if os.path.exists(target_path):
                        target_path = self.get_unique_path(target_path)

                    # 执行移动
                    shutil.move(file_path, target_path)

                    # 记录操作
                    operation = {
                        'type': 'move_file',
                        'operation': 'move',
                        'source_path': file_path,
                        'target_path': target_path,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功移动 {success_count} 个文件到 {target_folder}")
            if error_count > 0:
                self.log_message(f"移动失败 {error_count} 个文件")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量移动文件失败: {str(e)}")
            messagebox.showerror("错误", f"批量移动文件失败:\n{str(e)}")

    def move_multiple_folders_to_location(self, folders):
        """移动多个文件夹到其他位置"""
        try:
            if not folders:
                return

            # 选择目标位置
            target_location = filedialog.askdirectory(title="选择目标位置")
            if not target_location:
                return

            success_count = 0
            error_count = 0

            for item in folders:
                try:
                    folder_path = self.get_item_path(item)
                    if not folder_path or not os.path.exists(folder_path):
                        error_count += 1
                        continue

                    folder_name = os.path.basename(folder_path)
                    target_path = os.path.join(target_location, folder_name)

                    # 处理文件夹冲突
                    if os.path.exists(target_path):
                        target_path = self.get_unique_path(target_path)

                    # 执行移动
                    shutil.move(folder_path, target_path)

                    # 记录操作
                    operation = {
                        'type': 'move_folder',
                        'operation': 'move',
                        'source_path': folder_path,
                        'target_path': target_path,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功移动 {success_count} 个文件夹到 {target_location}")
            if error_count > 0:
                self.log_message(f"移动失败 {error_count} 个文件夹")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量移动文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"批量移动文件夹失败:\n{str(e)}")

    def move_multiple_items_up(self, items):
        """向上层移动多个项目（文件和文件夹混合）"""
        try:
            if not items:
                return

            # 确认操作
            result = messagebox.askyesno("确认移动",
                f"确定要将 {len(items)} 个项目向上层移动吗？")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in items:
                try:
                    item_path = self.get_item_path_safe(item)
                    if not item_path or not os.path.exists(item_path):
                        error_count += 1
                        continue

                    item_name = os.path.basename(item_path)
                    current_dir = os.path.dirname(item_path)
                    parent_dir = os.path.dirname(current_dir)

                    # 检查是否已经在根目录
                    if current_dir == parent_dir:
                        error_count += 1
                        continue

                    target_path = os.path.join(parent_dir, item_name)

                    # 处理冲突
                    if os.path.exists(target_path):
                        target_path = self.get_unique_path(target_path)

                    # 执行移动
                    shutil.move(item_path, target_path)

                    # 记录操作
                    operation = {
                        'type': 'move_item',
                        'operation': 'move',
                        'source_path': item_path,
                        'target_path': target_path,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功向上移动 {success_count} 个项目")
            if error_count > 0:
                self.log_message(f"移动失败 {error_count} 个项目")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量移动项目失败: {str(e)}")
            messagebox.showerror("错误", f"批量移动项目失败:\n{str(e)}")

    def delete_multiple_items(self, items):
        """删除多个项目（文件和文件夹混合）"""
        try:
            if not items:
                return

            # 统计项目信息
            files = []
            folders = []
            total_size = 0

            for item in items:
                item_path = self.get_item_path(item)
                if item_path and os.path.exists(item_path):
                    if os.path.isfile(item_path):
                        files.append(item_path)
                        try:
                            total_size += os.path.getsize(item_path)
                        except:
                            pass
                    elif os.path.isdir(item_path):
                        folders.append(item_path)
                        # 统计文件夹大小
                        for root, dirs, folder_files in os.walk(item_path):
                            for file in folder_files:
                                try:
                                    file_path = os.path.join(root, file)
                                    total_size += os.path.getsize(file_path)
                                except:
                                    pass

            if not files and not folders:
                messagebox.showerror("错误", "没有有效的项目可以删除")
                return

            # 确认删除
            info_parts = []
            if files:
                info_parts.append(f"{len(files)} 个文件")
            if folders:
                info_parts.append(f"{len(folders)} 个文件夹")

            result = messagebox.askyesno("确认删除",
                f"确定要删除 {', '.join(info_parts)} 吗？\n\n"
                f"总大小: {self.format_file_size(total_size)}\n\n"
                f"此操作不可撤销！")

            if not result:
                return

            success_count = 0
            error_count = 0

            for item in items:
                try:
                    item_path = self.get_item_path(item)
                    if not item_path or not os.path.exists(item_path):
                        error_count += 1
                        continue

                    item_name = os.path.basename(item_path)

                    # 执行删除
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)

                    # 记录操作
                    operation = {
                        'type': 'delete_item',
                        'operation': 'delete',
                        'source_path': item_path,
                        'name': item_name,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)

                    success_count += 1

                except Exception as e:
                    error_count += 1

            # 显示结果
            if success_count > 0:
                self.log_message(f"成功删除 {success_count} 个项目")
            if error_count > 0:
                self.log_message(f"删除失败 {error_count} 个项目")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"批量删除项目失败: {str(e)}")
            messagebox.showerror("错误", f"批量删除项目失败:\n{str(e)}")

    def has_subfolders(self, item):
        """检查指定项目是否包含子文件夹"""
        try:
            # 获取项目路径
            item_path = self.get_item_path(item)
            if not item_path or not os.path.isdir(item_path):
                return False

            # 检查是否有子文件夹
            for entry in os.listdir(item_path):
                entry_path = os.path.join(item_path, entry)
                if os.path.isdir(entry_path):
                    return True

            return False

        except Exception as e:
            self.log_message(f"检查子文件夹失败: {str(e)}")
            return False

    def get_item_path_safe(self, item):
        """安全获取树视图项目的实际文件路径（兼容预览和非预览模式）"""
        try:
            # 首先尝试使用原有的get_item_path方法（预览模式）
            if hasattr(self, 'preview_operations') and self.preview_operations:
                path = self.get_item_path(item)
                if path:
                    return path

            # 如果预览模式不可用，尝试从选中的文件夹构建路径
            if hasattr(self, 'selected_folder') and self.selected_folder.get():
                base_folder = self.selected_folder.get()

                # 构建路径：从根到当前项目
                path_parts = []
                current_item = item

                while current_item:
                    item_text = self.current_tree.item(current_item, 'text')

                    # 使用统一的名称提取方法
                    name = self.extract_clean_name(item_text)

                    if name:
                        # 跳过根文件夹（它已经在base_folder中）
                        parent = self.current_tree.parent(current_item)
                        if parent:  # 不是根项目
                            path_parts.insert(0, name)

                    current_item = self.current_tree.parent(current_item)

                # 构建完整路径
                if path_parts:
                    full_path = os.path.join(base_folder, *path_parts)
                else:
                    full_path = base_folder

                return full_path

            return None

        except Exception as e:
            self.log_message(f"安全获取路径失败: {str(e)}")
            import traceback
            self.log_message(f"错误详情: {traceback.format_exc()}")
            return None

    def extract_clean_name(self, item_text):
        """从树视图项目文本中提取干净的文件/文件夹名称"""
        try:
            import re

            # 清理输入文本，移除可能的Unicode变体选择符
            cleaned_text = item_text.strip()

            # 特殊处理：对于文件，需要保留文件名中的括号，只去掉最后的大小写信息
            # 例如：🖼️ 1 (294).jpg (468.1 KB) -> 1 (294).jpg

            # 首先尝试匹配带图标的文件格式：图标 + 可选变体选择符 + 空格 + 文件名 + 空格 + (大小)
            # 使用更宽泛的Unicode字符类来匹配emoji和变体选择符
            file_pattern = r'[\U0001F300-\U0001F9FF\uFE0F]*\s*(.+?)\s+\([0-9.]+\s*[KMGT]?B\)$'
            match = re.search(file_pattern, cleaned_text)
            if match:
                name = match.group(1).strip()
                # 清理可能残留的Unicode字符
                name = re.sub(r'[\uFE0F\u200D]', '', name)  # 移除变体选择符和零宽连接符
                if name:
                    return name

            # 然后尝试匹配带图标的文件夹格式：图标 + 可选变体选择符 + 空格 + 文件夹名 + 空格 + [统计信息]
            folder_pattern = r'[\U0001F300-\U0001F9FF\uFE0F]*\s*(.+?)(?:\s*\[.*?\])?$'
            match = re.search(folder_pattern, cleaned_text)
            if match:
                name = match.group(1).strip()
                # 清理可能残留的Unicode字符
                name = re.sub(r'[\uFE0F\u200D]', '', name)
                if name:
                    return name

            # 最后尝试通用模式（无图标）
            general_pattern = r'(.+?)(?:\s*\[.*?\]|\s+\([0-9.]+\s*[KMGT]?B\))?$'
            match = re.search(general_pattern, cleaned_text)
            if match:
                name = match.group(1).strip()
                # 清理可能残留的Unicode字符
                name = re.sub(r'[\uFE0F\u200D]', '', name)
                if name:
                    return name

            # 如果正则匹配失败，返回原始文本（去除首尾空格和特殊字符）
            fallback_name = cleaned_text
            fallback_name = re.sub(r'[\uFE0F\u200D]', '', fallback_name)
            return fallback_name

        except Exception as e:
            self.log_message(f"提取文件名失败: {str(e)}")
            return item_text.strip()

    def add_promote_option_to_menu(self, menu, items):
        """为任何菜单添加提升子文件夹选项 - 通用方法"""
        try:
            if not items:
                return

            # 确保items是列表
            if not isinstance(items, list):
                items = [items]

            menu.add_separator()

            if len(items) == 1:
                # 单个项目
                menu.add_command(label="📤 提升子文件夹层级",
                               command=lambda: self.promote_subfolders_simple(items[0]))
            else:
                # 多个项目
                menu.add_command(label=f"📤 批量提升子文件夹层级 ({len(items)} 个文件夹)",
                               command=lambda: self.batch_promote_subfolders(items))

            self.log_message(f"已为菜单添加提升选项，项目数: {len(items)}")

        except Exception as e:
            self.log_message(f"添加提升选项失败: {str(e)}")

    def promote_subfolders_simple(self, item):
        """简化版提升子文件夹层级功能"""
        try:
            # 获取当前文件夹路径
            current_folder_path = self.get_item_path(item)
            if not current_folder_path or not os.path.isdir(current_folder_path):
                messagebox.showerror("错误", "无法获取文件夹路径，请确保在预览模式下操作")
                return

            # 获取父文件夹路径
            parent_folder_path = os.path.dirname(current_folder_path)
            current_folder_name = os.path.basename(current_folder_path)

            # 获取所有子文件夹
            subfolders = []
            try:
                for entry in os.listdir(current_folder_path):
                    entry_path = os.path.join(current_folder_path, entry)
                    if os.path.isdir(entry_path):
                        subfolders.append((entry, entry_path))
            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件夹内容: {str(e)}")
                return

            if not subfolders:
                messagebox.showinfo("提示", "该文件夹中没有子文件夹")
                return

            # 确认操作
            subfolder_names = [name for name, _ in subfolders]
            message = f"确定要将以下子文件夹提升到上一层级吗？\n\n"
            message += f"当前位置：{current_folder_name}\n"
            message += f"目标位置：{os.path.basename(parent_folder_path)}\n\n"
            message += "子文件夹列表：\n"
            for name in subfolder_names[:10]:  # 最多显示10个
                message += f"• {name}\n"
            if len(subfolder_names) > 10:
                message += f"... 还有 {len(subfolder_names) - 10} 个文件夹\n"

            if not messagebox.askyesno("确认提升子文件夹层级", message):
                return

            # 执行提升操作
            success_count = 0
            failed_items = []

            for subfolder_name, subfolder_path in subfolders:
                try:
                    # 保存原始名称
                    original_name = subfolder_name
                    # 目标路径
                    target_path = os.path.join(parent_folder_path, subfolder_name)

                    # 检查目标位置是否已存在同名文件夹
                    if os.path.exists(target_path):
                        # 生成新名称
                        counter = 1
                        base_name = subfolder_name
                        while os.path.exists(target_path):
                            subfolder_name = f"{base_name}-{counter}"
                            target_path = os.path.join(parent_folder_path, subfolder_name)
                            counter += 1

                        self.log_message(f"目标位置已存在同名文件夹，重命名为: {subfolder_name}")

                    # 移动文件夹
                    shutil.move(subfolder_path, target_path)
                    success_count += 1
                    if original_name != subfolder_name:
                        self.log_message(f"成功提升子文件夹: {original_name} -> {subfolder_name}")
                    else:
                        self.log_message(f"成功提升子文件夹: {subfolder_name}")

                except Exception as e:
                    failed_items.append(f"{subfolder_name}: {str(e)}")
                    self.log_message(f"提升子文件夹失败 {subfolder_name}: {str(e)}")

            # 显示结果
            if success_count > 0:
                result_message = f"成功提升 {success_count} 个子文件夹"
                if failed_items:
                    result_message += f"\n失败 {len(failed_items)} 个:\n"
                    for failed in failed_items[:5]:  # 最多显示5个失败项
                        result_message += f"• {failed}\n"
                    if len(failed_items) > 5:
                        result_message += f"... 还有 {len(failed_items) - 5} 个失败项"

                messagebox.showinfo("提升完成", result_message)

                # 检查原文件夹是否为空，如果为空则询问是否删除
                try:
                    remaining_items = os.listdir(current_folder_path)
                    if not remaining_items:
                        if messagebox.askyesno("删除空文件夹",
                                             f"文件夹 '{current_folder_name}' 现在为空，是否删除？"):
                            os.rmdir(current_folder_path)
                            self.log_message(f"已删除空文件夹: {current_folder_name}")
                except Exception as e:
                    self.log_message(f"检查空文件夹失败: {str(e)}")

                # 刷新目录树
                self.refresh_folder_structure()
            else:
                messagebox.showerror("操作失败", "没有成功提升任何子文件夹")

        except Exception as e:
            self.log_message(f"提升子文件夹层级失败: {str(e)}")
            messagebox.showerror("错误", f"提升子文件夹层级失败:\n{str(e)}")

    def batch_promote_subfolders(self, folders):
        """批量提升多个文件夹的子文件夹层级"""
        try:
            if not folders:
                return

            # 确认操作
            folder_count = len(folders)
            if not messagebox.askyesno("确认批量提升",
                                     f"确定要对 {folder_count} 个文件夹执行子文件夹层级提升操作吗？"):
                return

            success_count = 0
            failed_count = 0

            for folder in folders:
                try:
                    # 对每个文件夹调用单个提升方法
                    self.promote_subfolders_simple(folder)
                    success_count += 1
                except Exception as e:
                    self.log_message(f"批量提升失败: {str(e)}")
                    failed_count += 1

            # 显示结果
            result_msg = f"批量提升完成\n成功: {success_count} 个文件夹\n失败: {failed_count} 个文件夹"
            if failed_count > 0:
                messagebox.showwarning("批量提升完成", result_msg)
            else:
                messagebox.showinfo("批量提升完成", result_msg)

        except Exception as e:
            self.log_message(f"批量提升子文件夹层级失败: {str(e)}")
            messagebox.showerror("错误", f"批量提升子文件夹层级失败:\n{str(e)}")

    def promote_subfolders(self, item):
        """提升子文件夹层级"""
        try:
            # 获取当前文件夹路径
            current_folder_path = self.get_item_path_safe(item)
            if not current_folder_path or not os.path.isdir(current_folder_path):
                messagebox.showerror("错误", "无法获取文件夹路径")
                return

            # 获取父文件夹路径
            parent_folder_path = os.path.dirname(current_folder_path)
            current_folder_name = os.path.basename(current_folder_path)

            # 获取所有子文件夹
            subfolders = []
            for entry in os.listdir(current_folder_path):
                entry_path = os.path.join(current_folder_path, entry)
                if os.path.isdir(entry_path):
                    subfolders.append((entry, entry_path))

            if not subfolders:
                messagebox.showinfo("提示", "该文件夹中没有子文件夹")
                return

            # 确认操作
            subfolder_names = [name for name, _ in subfolders]
            message = f"确定要将以下子文件夹提升到上一层级吗？\n\n"
            message += f"当前位置：{current_folder_name}\n"
            message += f"目标位置：{os.path.basename(parent_folder_path)}\n\n"
            message += "子文件夹列表：\n"
            for name in subfolder_names[:10]:  # 最多显示10个
                message += f"• {name}\n"
            if len(subfolder_names) > 10:
                message += f"... 还有 {len(subfolder_names) - 10} 个文件夹\n"

            if not messagebox.askyesno("确认提升子文件夹层级", message):
                return

            # 执行提升操作
            success_count = 0
            failed_items = []

            for subfolder_name, subfolder_path in subfolders:
                try:
                    # 保存原始名称
                    original_name = subfolder_name
                    # 目标路径
                    target_path = os.path.join(parent_folder_path, subfolder_name)

                    # 检查目标位置是否已存在同名文件夹
                    if os.path.exists(target_path):
                        # 生成新名称
                        counter = 1
                        base_name = subfolder_name
                        while os.path.exists(target_path):
                            subfolder_name = f"{base_name}-{counter}"
                            target_path = os.path.join(parent_folder_path, subfolder_name)
                            counter += 1

                        self.log_message(f"目标位置已存在同名文件夹，重命名为: {subfolder_name}")

                    # 移动文件夹
                    shutil.move(subfolder_path, target_path)
                    success_count += 1
                    if original_name != subfolder_name:
                        self.log_message(f"成功提升子文件夹: {original_name} -> {subfolder_name}")
                    else:
                        self.log_message(f"成功提升子文件夹: {subfolder_name}")

                except Exception as e:
                    failed_items.append(f"{subfolder_name}: {str(e)}")
                    self.log_message(f"提升子文件夹失败 {subfolder_name}: {str(e)}")

            # 显示结果
            if success_count > 0:
                result_message = f"成功提升 {success_count} 个子文件夹"
                if failed_items:
                    result_message += f"\n失败 {len(failed_items)} 个:\n"
                    for failed in failed_items[:5]:  # 最多显示5个失败项
                        result_message += f"• {failed}\n"
                    if len(failed_items) > 5:
                        result_message += f"... 还有 {len(failed_items) - 5} 个失败项"

                messagebox.showinfo("提升完成", result_message)

                # 检查原文件夹是否为空，如果为空则询问是否删除
                try:
                    remaining_items = os.listdir(current_folder_path)
                    if not remaining_items:
                        if messagebox.askyesno("删除空文件夹",
                                             f"文件夹 '{current_folder_name}' 现在为空，是否删除？"):
                            os.rmdir(current_folder_path)
                            self.log_message(f"已删除空文件夹: {current_folder_name}")
                except Exception as e:
                    self.log_message(f"检查空文件夹失败: {str(e)}")

                # 刷新目录树
                self.refresh_folder_structure()
            else:
                messagebox.showerror("操作失败", "没有成功提升任何子文件夹")

        except Exception as e:
            self.log_message(f"提升子文件夹层级失败: {str(e)}")
            messagebox.showerror("错误", f"提升子文件夹层级失败:\n{str(e)}")

    def show_filename_tooltip(self, event):
        """显示文件名工具提示"""
        try:
            # 获取鼠标位置的项目
            item = self.current_tree.identify_row(event.y)
            if not item:
                self.hide_filename_tooltip()
                return

            # 如果是同一个项目，不需要重新创建工具提示
            if item == self.tooltip_item:
                return

            # 隐藏之前的工具提示
            self.hide_filename_tooltip()

            # 获取项目文本
            item_text = self.current_tree.item(item, 'text')
            if not item_text:
                return

            # 提取文件名（去除图标和附加信息）
            import re
            name_match = re.search(r'[🎬📄🖼️🗑️📂📁]\s*(.+?)(?:\s*\[.*?\]|\s*\(.*?\))?$', item_text)
            if name_match:
                filename = name_match.group(1).strip()
            else:
                filename = item_text

            # 只有当文件名比较长时才显示工具提示
            if len(filename) > 30:  # 超过30个字符才显示
                # 创建工具提示窗口
                self.tooltip = tk.Toplevel(self.root)
                self.tooltip.wm_overrideredirect(True)
                self.tooltip.wm_geometry(f"+{event.x_root + 10}+{event.y_root + 10}")

                # 创建标签显示完整文件名
                label = tk.Label(self.tooltip,
                               text=filename,
                               background='lightyellow',
                               relief='solid',
                               borderwidth=1,
                               font=('Arial', 9),
                               wraplength=400)
                label.pack()

                self.tooltip_item = item

        except Exception as e:
            # 忽略工具提示错误，不影响主要功能
            pass

    def show_preview_filename_tooltip(self, event):
        """显示预览树文件名工具提示"""
        try:
            # 获取鼠标位置的项目
            item = self.preview_tree.identify_row(event.y)
            if not item:
                self.hide_filename_tooltip()
                return

            # 如果是同一个项目，不需要重新创建工具提示
            if item == self.tooltip_item:
                return

            # 隐藏之前的工具提示
            self.hide_filename_tooltip()

            # 获取项目文本
            item_text = self.preview_tree.item(item, 'text')
            if not item_text:
                return

            # 提取文件名（去除图标和附加信息）
            import re
            name_match = re.search(r'[🎬📄🖼️🗑️📂📁]\s*(.+?)(?:\s*\[.*?\]|\s*\(.*?\))?$', item_text)
            if name_match:
                filename = name_match.group(1).strip()
            else:
                filename = item_text

            # 只有当文件名比较长时才显示工具提示
            if len(filename) > 30:  # 超过30个字符才显示
                # 创建工具提示窗口
                self.tooltip = tk.Toplevel(self.root)
                self.tooltip.wm_overrideredirect(True)
                self.tooltip.wm_geometry(f"+{event.x_root + 10}+{event.y_root + 10}")

                # 创建标签显示完整文件名
                label = tk.Label(self.tooltip,
                               text=filename,
                               background='lightyellow',
                               relief='solid',
                               borderwidth=1,
                               font=('Arial', 9),
                               wraplength=400)
                label.pack()

                self.tooltip_item = item

        except Exception as e:
            # 忽略工具提示错误，不影响主要功能
            pass

    def hide_filename_tooltip(self, event=None):
        """隐藏文件名工具提示"""
        try:
            if self.tooltip:
                self.tooltip.destroy()
                self.tooltip = None
                self.tooltip_item = None
        except Exception as e:
            # 忽略工具提示错误
            pass

    def show_conflict_dialog(self, item_name, item_type="文件", target_location=""):
        """显示重名冲突处理对话框"""
        try:
            dialog = tk.Toplevel(self.root)
            dialog.title("重名冲突处理")
            dialog.geometry("500x400")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            result = {"action": "cancel", "new_name": "", "apply_to_all": False}

            # 主框架
            main_frame = ttk.Frame(dialog, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="⚠️ 发现重名冲突",
                                  font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # 冲突信息
            info_text = f"目标位置已存在同名{item_type}：\n\n"
            info_text += f"名称：{item_name}\n"
            if target_location:
                info_text += f"位置：{target_location}\n"
            info_text += f"\n请选择处理方式："

            info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
            info_label.pack(pady=(0, 20), anchor=tk.W)

            # 选项框架
            options_frame = ttk.LabelFrame(main_frame, text="处理选项", padding="10")
            options_frame.pack(fill=tk.X, pady=(0, 10))

            # 按钮框架
            buttons_frame = ttk.Frame(options_frame)
            buttons_frame.pack(fill=tk.X)

            # 第一行按钮
            row1_frame = ttk.Frame(buttons_frame)
            row1_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(row1_frame, text="🔄 自动重命名", width=15,
                      command=lambda: self._set_result(result, dialog, "auto_rename")).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(row1_frame, text="✏️ 手动重命名", width=15,
                      command=lambda: self._set_result(result, dialog, "manual_rename")).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(row1_frame, text="📝 覆盖", width=15,
                      command=lambda: self._set_result(result, dialog, "overwrite")).pack(side=tk.LEFT)

            # 第二行按钮
            row2_frame = ttk.Frame(buttons_frame)
            row2_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(row2_frame, text="⏭️ 跳过", width=15,
                      command=lambda: self._set_result(result, dialog, "skip")).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(row2_frame, text="🔗 合并", width=15,
                      command=lambda: self._set_result(result, dialog, "merge")).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(row2_frame, text="❌ 取消", width=15,
                      command=lambda: self._set_result(result, dialog, "cancel")).pack(side=tk.LEFT)

            # 手动重命名输入框（初始隐藏）
            self.rename_frame = ttk.LabelFrame(main_frame, text="输入新名称", padding="10")
            self.rename_entry = ttk.Entry(self.rename_frame, width=40)
            self.rename_entry.pack(fill=tk.X, pady=(0, 10))

            rename_buttons_frame = ttk.Frame(self.rename_frame)
            rename_buttons_frame.pack(fill=tk.X)

            ttk.Button(rename_buttons_frame, text="✅ 确认",
                      command=lambda: self._confirm_rename(result, dialog)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(rename_buttons_frame, text="❌ 取消",
                      command=lambda: self._cancel_rename()).pack(side=tk.LEFT)

            # 应用到所有复选框
            self.apply_all_var = tk.BooleanVar()
            apply_all_check = ttk.Checkbutton(main_frame, text="应用到所有后续冲突",
                                            variable=self.apply_all_var)
            apply_all_check.pack(pady=(10, 0))

            # 说明文本
            help_text = ("说明：\n"
                        "• 自动重命名：系统自动添加数字后缀（如：文件-1.txt）\n"
                        "• 手动重命名：您可以输入新的名称\n"
                        "• 覆盖：替换目标位置的同名项目\n"
                        "• 跳过：不处理此项目，继续下一个\n"
                        "• 合并：将内容合并到目标文件夹（仅文件夹）\n"
                        "• 取消：停止整个操作")

            help_label = ttk.Label(main_frame, text=help_text, font=('Arial', 8),
                                 foreground='gray', justify=tk.LEFT)
            help_label.pack(pady=(10, 0), anchor=tk.W)

            # 等待对话框关闭
            self.root.wait_window(dialog)

            return result["action"], result["new_name"], result["apply_to_all"]

        except Exception as e:
            self.log_message(f"显示冲突对话框失败: {str(e)}")
            return "auto_rename", "", False

    def _set_result(self, result, dialog, action):
        """设置对话框结果"""
        if action == "manual_rename":
            # 显示重命名输入框
            self.rename_frame.pack(fill=tk.X, pady=(10, 0))
            self.rename_entry.focus()
        else:
            result["action"] = action
            result["apply_to_all"] = self.apply_all_var.get()
            dialog.destroy()

    def _confirm_rename(self, result, dialog):
        """确认重命名"""
        new_name = self.rename_entry.get().strip()
        if not new_name:
            messagebox.showwarning("警告", "请输入新名称")
            return

        result["action"] = "manual_rename"
        result["new_name"] = new_name
        result["apply_to_all"] = self.apply_all_var.get()
        dialog.destroy()

    def _cancel_rename(self):
        """取消重命名"""
        self.rename_frame.pack_forget()

    def handle_conflict(self, item_name, item_type="文件", target_location=""):
        """统一的冲突处理方法"""
        # 检查是否有全局设置（用于批量操作）
        if hasattr(self, '_conflict_action') and self._conflict_action:
            if self._conflict_action == "auto_rename":
                return "auto_rename", "", False
            elif self._conflict_action == "overwrite":
                return "overwrite", "", False
            elif self._conflict_action == "skip":
                return "skip", "", False

        # 显示冲突对话框
        action, new_name, apply_to_all = self.show_conflict_dialog(
            item_name, item_type, target_location)

        # 如果用户选择应用到所有，保存设置
        if apply_to_all and action in ["auto_rename", "overwrite", "skip"]:
            self._conflict_action = action

        return action, new_name, apply_to_all

    def reset_conflict_settings(self):
        """重置冲突处理设置（用于新的操作）"""
        if hasattr(self, '_conflict_action'):
            delattr(self, '_conflict_action')

    def get_unique_path(self, path):
        """获取唯一的路径名（添加数字后缀）"""
        if not os.path.exists(path):
            return path

        base_path, ext = os.path.splitext(path)
        counter = 1

        while True:
            new_path = f"{base_path}-{counter}{ext}"
            if not os.path.exists(new_path):
                return new_path
            counter += 1

    def merge_folders(self, source_folder, target_folder):
        """合并文件夹内容"""
        try:
            for item in os.listdir(source_folder):
                source_item = os.path.join(source_folder, item)
                target_item = os.path.join(target_folder, item)

                if os.path.isfile(source_item):
                    # 处理文件
                    if os.path.exists(target_item):
                        target_item = self.get_unique_path(target_item)
                    shutil.move(source_item, target_item)
                elif os.path.isdir(source_item):
                    # 处理子文件夹
                    if os.path.exists(target_item):
                        self.merge_folders(source_item, target_item)
                    else:
                        shutil.move(source_item, target_item)

            # 删除空的源文件夹
            if not os.listdir(source_folder):
                os.rmdir(source_folder)

            self.log_message(f"文件夹已合并: {os.path.basename(source_folder)}")

        except Exception as e:
            self.log_message(f"合并文件夹失败: {str(e)}")
            raise

    def rename_file_with_folder_name(self, item):
        """按文件夹名重命名文件，将文件夹名作为文件名前缀"""
        try:
            file_path = self.get_item_path_safe(item)
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在或路径无效")
                return

            # 获取文件信息
            file_dir = os.path.dirname(file_path)
            file_name = os.path.basename(file_path)
            folder_name = os.path.basename(file_dir)

            # 分离文件名和扩展名
            name_without_ext, ext = os.path.splitext(file_name)

            # 检查文件名是否已经以文件夹名开头
            if name_without_ext.startswith(folder_name):
                # 即使已经有前缀，也允许用户编辑
                suggested_name = file_name
            else:
                # 构建建议的新文件名：文件夹名 + 原文件名
                suggested_name = f"{folder_name}_{file_name}"

            # 显示重命名对话框
            new_name = self.show_rename_dialog(file_name, suggested_name, folder_name)
            if not new_name or new_name == file_name:
                return  # 用户取消或没有更改

            # 构建新文件路径
            new_file_path = os.path.join(file_dir, new_name)

            # 检查新文件名是否已存在
            if os.path.exists(new_file_path):
                if not messagebox.askyesno("文件已存在",
                                         f"文件 '{new_name}' 已存在，是否覆盖？"):
                    return

            # 执行重命名
            os.rename(file_path, new_file_path)

            self.log_message(f"文件重命名成功: {file_name} -> {new_name}")

            # 刷新界面
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"重命名文件失败: {str(e)}")
            messagebox.showerror("错误", f"重命名文件失败: {str(e)}")

    def show_rename_dialog(self, original_name, suggested_name, folder_name):
        """显示重命名对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("重命名文件")
        dialog.geometry("500x200")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        result = [None]  # 使用列表来存储结果，以便在内部函数中修改

        # 创建界面
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 原文件名
        ttk.Label(main_frame, text="原文件名:").pack(anchor=tk.W)
        ttk.Label(main_frame, text=original_name, foreground="gray").pack(anchor=tk.W, pady=(0, 10))

        # 文件夹名提示
        ttk.Label(main_frame, text=f"文件夹名: {folder_name}").pack(anchor=tk.W, pady=(0, 5))

        # 新文件名输入
        ttk.Label(main_frame, text="新文件名:").pack(anchor=tk.W)
        name_var = tk.StringVar(value=suggested_name)
        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=60)
        name_entry.pack(fill=tk.X, pady=(0, 10))
        name_entry.select_range(0, tk.END)
        name_entry.focus()

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def on_ok():
            new_name = name_var.get().strip()
            if new_name and new_name != original_name:
                result[0] = new_name
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        def on_reset():
            name_var.set(suggested_name)
            name_entry.select_range(0, tk.END)

        # 按钮
        ttk.Button(button_frame, text="确定", command=on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=on_cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="重置", command=on_reset).pack(side=tk.LEFT)

        # 绑定回车键
        dialog.bind('<Return>', lambda e: on_ok())
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 等待对话框关闭
        dialog.wait_window()

        return result[0]

    def delete_file(self, item):
        """删除文件"""
        try:
            file_path = self.get_item_path_safe(item)
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在或路径无效")
                return

            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # 确认删除
            result = messagebox.askyesno("确认删除",
                f"确定要删除文件吗？\n\n"
                f"文件名: {file_name}\n"
                f"大小: {self.format_file_size(file_size)}\n\n"
                f"此操作不可撤销！")

            if not result:
                return

            # 执行删除
            os.remove(file_path)

            # 记录操作
            operation = {
                'type': 'delete_file',
                'operation': 'delete',
                'source_path': file_path,
                'name': file_name,
                'size': file_size,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件已删除: {file_name}")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"删除文件失败: {str(e)}")
            messagebox.showerror("错误", f"删除文件失败:\n{str(e)}")

    def delete_folder(self, item):
        """删除文件夹"""
        try:
            folder_path = self.get_item_path_safe(item)
            if not folder_path or not os.path.exists(folder_path):
                messagebox.showerror("错误", "文件夹不存在或路径无效")
                return

            folder_name = os.path.basename(folder_path)

            # 统计文件夹内容
            total_files = 0
            total_folders = 0
            total_size = 0

            for root, dirs, files in os.walk(folder_path):
                total_folders += len(dirs)
                total_files += len(files)
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                    except:
                        pass

            # 确认删除
            result = messagebox.askyesno("确认删除",
                f"确定要删除文件夹及其所有内容吗？\n\n"
                f"文件夹名: {folder_name}\n"
                f"包含: {total_files} 个文件, {total_folders} 个子文件夹\n"
                f"总大小: {self.format_file_size(total_size)}\n\n"
                f"此操作不可撤销！")

            if not result:
                return

            # 执行删除
            shutil.rmtree(folder_path)

            # 记录操作
            operation = {
                'type': 'delete_folder',
                'operation': 'delete',
                'source_path': folder_path,
                'folder_name': folder_name,
                'file_count': total_files,
                'folder_count': total_folders,
                'total_size': total_size,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件夹已删除: {folder_name} ({total_files}个文件, {total_folders}个子文件夹)")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"删除文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"删除文件夹失败:\n{str(e)}")

    def rename_folder(self, item):
        """重命名文件夹"""
        try:
            folder_path = self.get_item_path_safe(item)
            if not folder_path or not os.path.exists(folder_path):
                messagebox.showerror("错误", "文件夹不存在或路径无效")
                return

            old_name = os.path.basename(folder_path)
            parent_dir = os.path.dirname(folder_path)

            # 输入新名称
            new_name = simpledialog.askstring("重命名文件夹",
                f"请输入新的文件夹名称:\n\n当前名称: {old_name}",
                initialvalue=old_name)

            if not new_name or new_name == old_name:
                return

            # 检查新名称是否有效
            if any(char in new_name for char in ['<', '>', ':', '"', '|', '?', '*']):
                messagebox.showerror("错误", "文件夹名称包含无效字符")
                return

            new_path = os.path.join(parent_dir, new_name)

            # 检查新名称是否已存在
            if os.path.exists(new_path):
                messagebox.showerror("错误", f"文件夹名称 '{new_name}' 已存在")
                return

            # 执行重命名
            os.rename(folder_path, new_path)

            # 记录操作
            operation = {
                'type': 'rename_folder',
                'operation': 'rename',
                'source_path': folder_path,
                'target_path': new_path,
                'old_name': old_name,
                'new_name': new_name,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件夹已重命名: {old_name} → {new_name}")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"重命名文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"重命名文件夹失败:\n{str(e)}")

    def move_file_to_folder(self, item):
        """移动文件到其他文件夹"""
        try:
            file_path = self.get_item_path_safe(item)
            if not file_path or not os.path.exists(file_path):
                messagebox.showerror("错误", "文件不存在或路径无效")
                return

            file_name = os.path.basename(file_path)
            source_dir = os.path.dirname(file_path)

            # 选择目标文件夹
            target_folder = filedialog.askdirectory(title="选择目标文件夹")
            if not target_folder:
                return

            target_path = os.path.join(target_folder, file_name)

            # 检查目标是否已存在
            if os.path.exists(target_path):
                result = messagebox.askyesnocancel("文件冲突",
                    f"目标文件夹已存在文件 '{file_name}'，是否重命名移动？\n\n"
                    f"是：重命名移动\n"
                    f"否：覆盖现有文件\n"
                    f"取消：取消操作")

                if result is None:  # 取消
                    return
                elif result:  # 重命名
                    target_path = self.get_unique_path(target_path)

            # 执行移动
            shutil.move(file_path, target_path)

            # 记录操作
            operation = {
                'type': 'move_file',
                'operation': 'move',
                'source_path': file_path,
                'target_path': target_path,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件已移动: {file_name} → {target_folder}")

            # 检查源文件夹是否为空，如果为空则询问是否删除
            self.check_and_prompt_delete_empty_folder(source_dir)

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"移动文件失败: {str(e)}")
            messagebox.showerror("错误", f"移动文件失败:\n{str(e)}")

    def move_folder_to_location(self, item):
        """移动文件夹到其他位置"""
        try:
            folder_path = self.get_item_path_safe(item)
            if not folder_path or not os.path.exists(folder_path):
                messagebox.showerror("错误", "文件夹不存在或路径无效")
                return

            folder_name = os.path.basename(folder_path)

            # 选择目标位置
            target_location = filedialog.askdirectory(title="选择目标位置")
            if not target_location:
                return

            target_path = os.path.join(target_location, folder_name)

            # 检查目标是否已存在
            if os.path.exists(target_path):
                result = messagebox.askyesnocancel("文件夹冲突",
                    f"目标位置已存在文件夹 '{folder_name}'，是否重命名移动？\n\n"
                    f"是：重命名移动\n"
                    f"否：合并文件夹\n"
                    f"取消：取消操作")

                if result is None:  # 取消
                    return
                elif result:  # 重命名
                    target_path = self.get_unique_path(target_path)
                else:  # 合并
                    self.merge_folders(folder_path, target_path)
                    return

            # 执行移动
            shutil.move(folder_path, target_path)

            # 记录操作
            operation = {
                'type': 'move_folder',
                'operation': 'move',
                'source_path': folder_path,
                'target_path': target_path,
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            self.log_message(f"文件夹已移动: {folder_name} → {target_location}")

            # 刷新目录结构
            self.refresh_folder_structure()

        except Exception as e:
            self.log_message(f"移动文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"移动文件夹失败:\n{str(e)}")
        if item:
            item_text = self.preview_tree.item(item, 'text')
            messagebox.showinfo("预览", f"预览项: {item_text}\n\n这是整理后的预览结果。\n可以通过右侧的设置来调整分类方式。")

    def next_folder_preview(self):
        """显示下一个文件夹的预览"""
        # 保存当前文件夹的设置
        current_operation = self.preview_operations[self.current_preview_index]
        self.preview_results.append({
            'index': self.current_preview_index,
            'operation': current_operation.copy()
        })

        self.current_preview_index += 1
        self.create_single_folder_preview()

    def prev_folder_preview(self):
        """显示上一个文件夹的预览"""
        if self.current_preview_index > 0:
            self.current_preview_index -= 1
            self.create_single_folder_preview()

    def reset_current_folder(self):
        """重置当前文件夹为智能建议"""
        current_operation = self.preview_operations[self.current_preview_index]
        current_operation['user_actions'] = current_operation['suggested_actions'].copy()
        self.create_single_folder_preview()

    def finish_preview(self):
        """完成预览，显示汇总"""
        # 保存最后一个文件夹的设置
        current_operation = self.preview_operations[self.current_preview_index]
        self.preview_results.append({
            'index': self.current_preview_index,
            'operation': current_operation.copy()
        })

        self.show_preview_summary()

    def show_preview_summary(self):
        """显示预览汇总并确认开始整理"""
        if self.preview_window:
            self.preview_window.destroy()

        # 创建汇总窗口
        summary_window = tk.Toplevel(self.root)
        summary_window.title("预览汇总 - 确认整理")
        summary_window.geometry("700x500")
        summary_window.resizable(True, True)

        # 居中显示
        summary_window.update_idletasks()
        width = summary_window.winfo_width()
        height = summary_window.winfo_height()
        x = (summary_window.winfo_screenwidth() // 2) - (width // 2)
        y = (summary_window.winfo_screenheight() // 2) - (height // 2)
        summary_window.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = ttk.Frame(summary_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📋 预览汇总", font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 15))

        # 创建滚动文本区域显示汇总
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        summary_text = tk.Text(text_frame, wrap=tk.WORD, height=20)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=summary_text.yview)
        summary_text.configure(yscrollcommand=scrollbar.set)

        summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 生成汇总内容
        summary_content = self.generate_summary_content()
        summary_text.insert(tk.END, summary_content)
        summary_text.config(state=tk.DISABLED)

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="返回修改",
                  command=lambda: [summary_window.destroy(), self.return_to_preview()]).pack(side=tk.LEFT)

        ttk.Button(button_frame, text="取消",
                  command=lambda: [summary_window.destroy(), self.close_preview_window()]).pack(side=tk.RIGHT, padx=(10, 0))

        ttk.Button(button_frame, text="确认开始整理",
                  command=lambda: [summary_window.destroy(), self.start_organizing_with_preview()]).pack(side=tk.RIGHT, padx=(10, 0))

    def generate_summary_content(self):
        """生成预览汇总内容"""
        content = "整理计划汇总\n"
        content += "=" * 50 + "\n\n"

        total_folders = len(self.preview_operations)
        total_video = 0
        total_image = 0
        total_other = 0
        total_small = 0

        for i, operation in enumerate(self.preview_operations):
            folder_name = operation['folder_name']
            user_actions = operation['user_actions']

            video_count = len(operation['video_files'])
            image_count = len(operation['image_folders'])
            other_count = len(operation['other_files'])
            small_count = len(operation['small_files'])

            total_video += video_count
            total_image += image_count
            total_other += other_count
            total_small += small_count

            content += f"{i+1}. 📁 {folder_name}\n"

            if video_count > 0:
                action = user_actions.get('video_action', 'auto')
                action_desc = self.get_action_description(action)
                content += f"   📹 视频文件 ({video_count}个): {action_desc}\n"

            if image_count > 0:
                action = user_actions.get('image_action', 'auto')
                action_desc = self.get_action_description(action)
                content += f"   🖼️ 图片文件夹 ({image_count}个): {action_desc}\n"

            if other_count > 0:
                action = user_actions.get('other_action', 'auto')
                action_desc = self.get_action_description(action)
                content += f"   📄 其他文件 ({other_count}个): {action_desc}\n"

            if small_count > 0:
                action = user_actions.get('small_action', 'delete')
                action_desc = self.get_action_description(action)
                content += f"   🗑️ 小文件 ({small_count}个): {action_desc}\n"

            content += "\n"

        content += "=" * 50 + "\n"
        content += "统计汇总:\n"
        content += f"📂 总文件夹数: {total_folders}\n"
        content += f"📹 视频文件总数: {total_video}\n"
        content += f"🖼️ 图片文件夹总数: {total_image}\n"
        content += f"📄 其他文件总数: {total_other}\n"
        content += f"🗑️ 小文件总数: {total_small}\n"
        content += "=" * 50 + "\n\n"
        content += "点击'确认开始整理'将按照上述计划执行整理操作。\n"
        content += "所有操作都会被记录，可以使用'还原操作'功能撤销。"

        return content

    def get_action_description(self, action):
        """获取操作的中文描述"""
        action_map = {
            'auto': '自动处理',
            'keep': '保留在原位置',
            'move_to_video': '移动到视频文件夹',
            'move_to_image': '移动到图片文件夹',
            'move_to_other': '移动到其他文件夹',
            'delete': '删除'
        }
        return action_map.get(action, '未知操作')

    def return_to_preview(self):
        """返回到预览界面"""
        # 重置到第一个文件夹
        self.current_preview_index = 0
        self.create_single_folder_preview()

    def apply_preview_changes(self):
        """应用预览更改并开始整理（保留兼容性）"""
        # 关闭预览窗口
        self.close_preview_window()

        # 使用修改后的操作开始整理
        self.start_organizing_with_preview()



    def reset_to_suggestions(self):
        """重置为智能建议"""
        for operation in self.preview_operations:
            operation['user_actions'] = operation['suggested_actions'].copy()

        # 重新创建预览窗口
        self.create_preview_window()

    def close_preview_window(self):
        """关闭预览窗口"""
        if self.preview_window:
            self.preview_window.destroy()
            self.preview_window = None

    def start_organizing_with_preview(self):
        """使用预览设置开始整理"""
        if not self.preview_operations:
            messagebox.showwarning("警告", "没有预览数据！")
            return

        # 确认对话框
        total_folders = len(self.preview_operations)
        result = messagebox.askyesno("确认整理",
            f"确定要按照预览设置整理 {total_folders} 个文件夹吗？\n"
            "所有操作都会被记录，可以还原。")

        if result:

            self.is_running = True
            self.should_stop = False
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

            # 在新线程中执行整理操作
            thread = threading.Thread(target=self.organize_files_with_preview)
            thread.daemon = True
            thread.start()

    def organize_files_with_preview(self):
        """使用预览设置整理文件"""
        try:
            self.status_var.set("正在按预览设置整理文件...")
            self.log_message("开始按预览设置整理文件...")

            total_folders = len(self.preview_operations)

            for i, operation in enumerate(self.preview_operations):
                if self.should_stop:
                    break

                folder_name = operation['folder_name']
                self.status_var.set(f"正在处理: {folder_name} ({i+1}/{total_folders})")
                self.log_message(f"\n开始处理文件夹: {folder_name}")

                # 验证预览与实际操作的一致性
                self.validate_preview_consistency(operation)

                # 根据用户设置处理文件
                self.process_folder_with_user_actions(operation)

                # 更新进度
                progress = ((i + 1) / total_folders) * 100
                self.progress_var.set(progress)

            # 保存操作日志
            self.save_operation_log()

            self.status_var.set("整理完成")
            self.log_message("按预览设置整理完成！")

            if not self.should_stop:
                messagebox.showinfo("完成", f"文件整理完成！\n处理了 {total_folders} 个文件夹")

        except Exception as e:
            self.log_message(f"整理过程中出现错误: {str(e)}")
            messagebox.showerror("错误", f"整理过程中出现错误:\n{str(e)}")

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.progress_var.set(0)

    def process_folder_with_user_actions(self, operation):
        """根据用户设置处理单个文件夹（按优先级顺序逐一处理）"""
        folder_path = operation['folder_path']
        folder_name = operation['folder_name']
        user_actions = operation['user_actions']

        self.log_message(f"开始按优先级顺序处理文件夹: {folder_name}")

        # 第一步：处理小文件（优先删除，避免影响其他操作）
        if operation['small_files']:
            self.log_message("第1步：处理小文件")
            self.process_small_files_by_action(operation['small_files'], user_actions.get('small_action', 'delete'))

        # 第二步：处理图片文件夹（保持原有结构，避免与单独图片文件冲突）
        if operation['image_folders']:
            image_action = user_actions.get('image_action', 'keep')
            if image_action == 'skip':
                self.log_message("第2步：跳过图片文件夹处理")
            else:
                self.log_message("第2步：处理图片文件夹")
                # 创建必要的目标文件夹（仅用于图片文件夹）
                target_folders = self.create_target_folders_for_image_folders(folder_path, user_actions)
                self.process_image_folders_by_action(operation['image_folders'], image_action,
                                                   target_folders, folder_name)

        # 第三步：处理视频文件（可能创建本层视频文件夹）
        if operation['video_files']:
            self.log_message("第3步：处理视频文件")
            video_action = user_actions.get('video_action', 'move_to_local_video')
            if video_action == 'move_to_local_video':
                # 本层处理，不需要创建全局目标文件夹
                self.process_files_by_action(operation['video_files'], video_action, {}, 'video', folder_name)
            else:
                # 需要全局目标文件夹
                target_folders = self.create_target_folders_for_videos(folder_path, user_actions)
                self.process_files_by_action(operation['video_files'], video_action, target_folders, 'video', folder_name)

        # 第四步：处理单独图片文件（可能创建本层图片文件夹）
        if operation.get('image_files', []):
            image_file_action = user_actions.get('image_file_action', self.image_settings.get('single_image_action', 'move_to_local_image'))
            if image_file_action == 'skip':
                self.log_message("第4步：跳过单独图片文件处理")
            else:
                self.log_message("第4步：处理单独图片文件")
                if image_file_action == 'move_to_local_image':
                    # 本层处理，不需要创建全局目标文件夹
                    self.process_files_by_action(operation.get('image_files', []), image_file_action, {}, 'image_file', folder_name)
                else:
                    # 需要全局目标文件夹
                    target_folders = self.create_target_folders_for_images(folder_path, user_actions)
                    self.process_files_by_action(operation.get('image_files', []), image_file_action, target_folders, 'image_file', folder_name)

        # 第五步：处理其他文件
        if operation['other_files']:
            self.log_message("第5步：处理其他文件")
            target_folders = self.create_target_folders_for_others(folder_path, user_actions)
            self.process_files_by_action(operation['other_files'], user_actions.get('other_action', 'keep'),
                                       target_folders, 'other', folder_name)

        # 第六步：清理空文件夹
        self.log_message("第6步：清理空文件夹")
        self.clean_empty_folders(folder_path)

    def create_target_folders_from_actions(self, base_path, user_actions):
        """根据用户操作创建目标文件夹"""
        folders = {
            'video': base_path,
            'image': base_path,
            'other': base_path
        }

        # 检查是否需要创建分类文件夹
        need_video_folder = (user_actions.get('video_action') == 'move_to_video' or
                           user_actions.get('other_action') == 'move_to_video')
        need_image_folder = (user_actions.get('image_action') == 'move_to_image' or
                           user_actions.get('video_action') == 'move_to_image' or
                           user_actions.get('other_action') == 'move_to_image' or
                           user_actions.get('image_file_action') == 'move_to_image')
        need_other_folder = (user_actions.get('other_action') == 'move_to_other' or
                           user_actions.get('video_action') == 'move_to_other' or
                           user_actions.get('image_action') == 'move_to_other' or
                           user_actions.get('small_action') == 'move_to_other')

        if need_video_folder:
            folders['video'] = os.path.join(base_path, '视频')
            if not os.path.exists(folders['video']):
                os.makedirs(folders['video'])
                self.log_message(f"  创建视频文件夹")

        if need_image_folder:
            folders['image'] = os.path.join(base_path, '图片')
            if not os.path.exists(folders['image']):
                os.makedirs(folders['image'])
                self.log_message(f"  创建图片文件夹")

        if need_other_folder:
            folders['other'] = os.path.join(base_path, '其他')
            if not os.path.exists(folders['other']):
                os.makedirs(folders['other'])
                self.log_message(f"  创建其他文件夹")

        return folders

    def create_target_folders_for_image_folders(self, base_path, user_actions):
        """为图片文件夹创建目标文件夹"""
        folders = {'image': base_path, 'other': base_path}

        image_action = user_actions.get('image_action', 'keep')
        if image_action == 'move_to_image':
            folders['image'] = os.path.join(base_path, '图片')
            if not os.path.exists(folders['image']):
                os.makedirs(folders['image'])
        elif image_action == 'move_to_other':
            folders['other'] = os.path.join(base_path, '其他')
            if not os.path.exists(folders['other']):
                os.makedirs(folders['other'])

        return folders

    def create_target_folders_for_videos(self, base_path, user_actions):
        """为视频文件创建目标文件夹"""
        folders = {'video': base_path, 'other': base_path}

        video_action = user_actions.get('video_action', 'keep')
        if video_action == 'move_to_video':
            folders['video'] = os.path.join(base_path, '视频')
            if not os.path.exists(folders['video']):
                os.makedirs(folders['video'])
        elif video_action == 'move_to_other':
            folders['other'] = os.path.join(base_path, '其他')
            if not os.path.exists(folders['other']):
                os.makedirs(folders['other'])

        return folders

    def create_target_folders_for_images(self, base_path, user_actions):
        """为单独图片文件创建目标文件夹"""
        folders = {'image': base_path, 'other': base_path}

        image_file_action = user_actions.get('image_file_action', 'keep')
        if image_file_action == 'move_to_image':
            folders['image'] = os.path.join(base_path, '图片')
            if not os.path.exists(folders['image']):
                os.makedirs(folders['image'])
        elif image_file_action == 'move_to_other':
            folders['other'] = os.path.join(base_path, '其他')
            if not os.path.exists(folders['other']):
                os.makedirs(folders['other'])

        return folders

    def create_target_folders_for_others(self, base_path, user_actions):
        """为其他文件创建目标文件夹"""
        folders = {'video': base_path, 'image': base_path, 'other': base_path}

        other_action = user_actions.get('other_action', 'keep')
        if other_action == 'move_to_video':
            folders['video'] = os.path.join(base_path, '视频')
            if not os.path.exists(folders['video']):
                os.makedirs(folders['video'])
        elif other_action == 'move_to_image':
            folders['image'] = os.path.join(base_path, '图片')
            if not os.path.exists(folders['image']):
                os.makedirs(folders['image'])
        elif other_action == 'move_to_other':
            folders['other'] = os.path.join(base_path, '其他')
            if not os.path.exists(folders['other']):
                os.makedirs(folders['other'])

        return folders

    def process_files_by_action(self, files, action, target_folders, file_type, folder_name):
        """根据用户操作处理文件"""
        if not files:
            return

        # 对于图片文件，使用更友好的显示名称
        display_name = "图片文件" if file_type == 'image_file' else f"{file_type}文件"

        if action == 'keep':
            self.log_message(f"  保持 {len(files)} 个{display_name}在原位置")
            return
        elif action == 'move_to_root':
            # 移动到根目录（子文件夹的根目录）
            # 获取当前操作的文件夹信息来确定根目录
            current_operation = None
            for operation in self.preview_operations:
                if operation['folder_name'] == folder_name:
                    current_operation = operation
                    break

            if current_operation:
                # 根目录就是子文件夹本身
                target_folder = current_operation['folder_path']
            else:
                # 备用方案：使用当前处理的文件夹
                target_folder = target_folders.get('video', target_folders.get('other', target_folders.get('image')))
            self.log_message(f"  移动 {len(files)} 个{display_name}到子文件夹根目录")
        elif action == 'normalize_filename':
            # 规范化文件名
            self.log_message(f"  规范化 {len(files)} 个{display_name}的文件名")
            # 找到当前文件夹的索引
            folder_index = self.get_folder_index_by_name(folder_name)
            self.normalize_files_list(files, folder_index)
            return  # 规范化不需要移动文件
        elif action == 'move_to_video':
            target_folder = target_folders['video']
            self.log_message(f"  移动 {len(files)} 个{display_name}到视频文件夹")
        elif action == 'move_to_local_video':
            # 在文件所在的同一层级创建视频文件夹 - 需要特殊处理
            self.process_files_to_local_video_folders(files, display_name)
            return  # 特殊处理，直接返回
        elif action == 'move_to_image':
            target_folder = target_folders['image']
            self.log_message(f"  移动 {len(files)} 个{display_name}到图片文件夹")
        elif action == 'move_to_local_image':
            # 在文件所在的同一层级创建图片文件夹 - 需要特殊处理
            self.process_files_to_local_image_folders(files, display_name)
            return  # 特殊处理，直接返回
        elif action == 'move_to_other':
            target_folder = target_folders['other']
            self.log_message(f"  移动 {len(files)} 个{display_name}到其他文件夹")
        elif action == 'delete':
            # 删除其他文件
            self.log_message(f"  删除 {len(files)} 个{display_name}")
            self.delete_files_by_action(files, file_type)
            return
        else:  # auto
            # 使用默认逻辑
            target_folder = self.get_auto_target_folder(files, target_folders, file_type, folder_name)
            if target_folder is None:
                self.log_message(f"  保持 {len(files)} 个{display_name}在原位置")
                return

        # 执行文件移动
        for file_info in files:
            if self.should_stop:
                break
            self.move_file(file_info, target_folder)

    def process_files_to_local_image_folders(self, files, display_name):
        """将图片文件移动到各自所在目录的图片文件夹中（严格本层处理）"""
        if not files:
            return

        self.log_message(f"  移动 {len(files)} 个{display_name}到各自本层图片文件夹")

        # 按文件所在目录分组
        files_by_dir = {}
        for file_info in files:
            file_path = file_info['path']
            file_dir = os.path.dirname(file_path)

            # 验证文件确实存在且在预期位置
            if not os.path.exists(file_path):
                self.log_message(f"    警告：文件不存在，跳过: {file_path}")
                continue

            if file_dir not in files_by_dir:
                files_by_dir[file_dir] = []
            files_by_dir[file_dir].append(file_info)

        # 为每个目录创建图片文件夹并移动文件
        for file_dir, dir_files in files_by_dir.items():
            # 确保目录存在
            if not os.path.exists(file_dir):
                self.log_message(f"    警告：目录不存在，跳过: {file_dir}")
                continue

            # 在当前目录创建"图片"文件夹
            image_folder_path = os.path.join(file_dir, "图片")

            # 验证不会创建循环引用
            if image_folder_path == file_dir:
                self.log_message(f"    错误：避免循环引用，跳过: {file_dir}")
                continue

            # 如果图片文件夹不存在，创建它
            if not os.path.exists(image_folder_path):
                try:
                    os.makedirs(image_folder_path)
                    relative_path = os.path.relpath(image_folder_path, self.selected_folder.get())
                    self.log_message(f"    创建本层图片文件夹: {relative_path}")

                    # 记录创建文件夹的操作
                    operation = {
                        'type': 'create_folder',
                        'operation': 'create',
                        'path': image_folder_path,
                        'folder_name': '图片',
                        'parent_folder': file_dir,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)
                except Exception as e:
                    self.log_message(f"    错误：无法创建图片文件夹 {image_folder_path}: {str(e)}")
                    continue

            # 移动该目录下的所有图片文件
            for file_info in dir_files:
                if self.should_stop:
                    break

                # 再次验证文件存在
                if os.path.exists(file_info['path']):
                    self.move_file(file_info, image_folder_path)
                else:
                    self.log_message(f"    警告：文件已不存在，跳过移动: {file_info['path']}")

    def process_files_to_local_video_folders(self, files, display_name):
        """将视频文件移动到各自所在目录的视频文件夹中（严格本层处理）"""
        if not files:
            return

        self.log_message(f"  移动 {len(files)} 个{display_name}到各自本层视频文件夹")

        # 按文件所在目录分组
        files_by_dir = {}
        for file_info in files:
            file_path = file_info['path']
            file_dir = os.path.dirname(file_path)

            # 验证文件确实存在且在预期位置
            if not os.path.exists(file_path):
                self.log_message(f"    警告：文件不存在，跳过: {file_path}")
                continue

            if file_dir not in files_by_dir:
                files_by_dir[file_dir] = []
            files_by_dir[file_dir].append(file_info)

        # 为每个目录创建视频文件夹并移动文件
        for file_dir, dir_files in files_by_dir.items():
            # 确保目录存在
            if not os.path.exists(file_dir):
                self.log_message(f"    警告：目录不存在，跳过: {file_dir}")
                continue

            # 在当前目录创建"视频"文件夹
            video_folder_path = os.path.join(file_dir, "视频")

            # 验证不会创建循环引用
            if video_folder_path == file_dir:
                self.log_message(f"    错误：避免循环引用，跳过: {file_dir}")
                continue

            # 如果视频文件夹不存在，创建它
            if not os.path.exists(video_folder_path):
                try:
                    os.makedirs(video_folder_path)
                    relative_path = os.path.relpath(video_folder_path, self.selected_folder.get())
                    self.log_message(f"    创建本层视频文件夹: {relative_path}")

                    # 记录创建文件夹的操作
                    operation = {
                        'type': 'create_folder',
                        'operation': 'create',
                        'path': video_folder_path,
                        'folder_name': '视频',
                        'parent_folder': file_dir,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.operation_log.append(operation)
                except Exception as e:
                    self.log_message(f"    错误：无法创建视频文件夹 {video_folder_path}: {str(e)}")
                    continue

            # 移动该目录下的所有视频文件
            for file_info in dir_files:
                if self.should_stop:
                    break

                # 再次验证文件存在
                if os.path.exists(file_info['path']):
                    self.move_file(file_info, video_folder_path)
                else:
                    self.log_message(f"    警告：文件已不存在，跳过移动: {file_info['path']}")

    def delete_files_by_action(self, files, file_type):
        """删除指定的文件"""
        deleted_count = 0
        failed_count = 0

        for file_info in files:
            if self.should_stop:
                break

            try:
                file_path = file_info['path']
                file_name = file_info['name']

                if os.path.exists(file_path):
                    # 记录删除操作
                    self.record_operation('delete_file', file_path, None, {
                        'file_name': file_name,
                        'file_size': file_info['size'],
                        'file_type': file_type
                    })

                    # 删除文件
                    os.remove(file_path)
                    deleted_count += 1
                    self.log_message(f"    删除{file_type}文件: {file_name}")
                else:
                    self.log_message(f"    文件不存在，跳过: {file_name}")
                    failed_count += 1

            except Exception as e:
                self.log_message(f"    删除文件失败: {file_info['name']} - {str(e)}")
                failed_count += 1

        if deleted_count > 0:
            self.log_message(f"  成功删除 {deleted_count} 个{file_type}文件")
        if failed_count > 0:
            self.log_message(f"  删除失败 {failed_count} 个{file_type}文件")

    def get_auto_target_folder(self, files, target_folders, file_type, folder_name):
        """获取自动处理的目标文件夹"""
        # 获取当前操作的文件夹信息
        current_operation = None
        for operation in self.preview_operations:
            if operation['folder_name'] == folder_name:
                current_operation = operation
                break

        if not current_operation:
            # 如果找不到操作信息，使用默认逻辑
            if file_type == 'video':
                return target_folders.get('video')
            elif file_type == 'image_file':
                return target_folders.get('image', target_folders.get('other'))
            else:
                return target_folders.get('other')

        # 使用默认判断逻辑
        file_types_count = 0
        if current_operation['video_files']:
            file_types_count += 1
        if current_operation['image_folders']:
            file_types_count += 1
        if current_operation.get('image_files', []):  # 新增：单独图片文件
            file_types_count += 1
        if current_operation['other_files']:
            file_types_count += 1

        # 对于图片文件，使用更友好的显示名称
        display_name = "图片文件" if file_type == 'image_file' else f"{file_type}文件"

        if file_types_count <= 1:
            # 只有一种文件类型，移动到根目录（子文件夹的根目录）
            # 根目录就是子文件夹本身
            root_folder = current_operation['folder_path']
            self.log_message(f"  默认判断：只有一种文件类型，移动 {len(files)} 个{display_name}到子文件夹根目录")
            return root_folder
        else:
            # 多种文件类型，创建分类文件夹
            if file_type == 'video':
                target_folder = target_folders.get('video')
                self.log_message(f"  默认判断：多种文件类型，移动 {len(files)} 个{display_name}到视频文件夹")
            elif file_type == 'image_file':
                target_folder = target_folders.get('image', target_folders.get('other'))
                folder_name = "图片文件夹" if target_folder == target_folders.get('image') else "其他文件夹"
                self.log_message(f"  默认判断：多种文件类型，移动 {len(files)} 个{display_name}到{folder_name}")
            else:
                target_folder = target_folders.get('other')
                self.log_message(f"  默认判断：多种文件类型，移动 {len(files)} 个{display_name}到其他文件夹")
            return target_folder

    def process_image_folders_by_action(self, image_folders, action, target_folders, folder_name):
        """根据用户操作处理图片文件夹"""
        if not image_folders:
            return

        if action == 'keep':
            self.log_message(f"  保持 {len(image_folders)} 个图片文件夹在原位置")
            return
        elif action == 'move_to_root':
            # 移动到根目录（子文件夹的根目录）
            # 获取当前操作的文件夹信息来确定根目录
            current_operation = None
            for operation in self.preview_operations:
                if operation['folder_name'] == folder_name:
                    current_operation = operation
                    break

            if current_operation:
                # 根目录就是子文件夹本身
                target_folder = current_operation['folder_path']
            else:
                # 备用方案：使用当前处理的文件夹
                target_folder = target_folders.get('image', target_folders.get('video', target_folders.get('other')))
            self.log_message(f"  移动 {len(image_folders)} 个图片文件夹到子文件夹根目录")
        elif action == 'move_to_video':
            target_folder = target_folders['video']
            self.log_message(f"  移动 {len(image_folders)} 个图片文件夹到视频文件夹")
        elif action == 'move_to_image':
            target_folder = target_folders['image']
            self.log_message(f"  移动 {len(image_folders)} 个图片文件夹到图片文件夹")
        elif action == 'move_to_other':
            target_folder = target_folders['other']
            self.log_message(f"  移动 {len(image_folders)} 个图片文件夹到其他文件夹")
        else:  # auto
            # 使用默认逻辑
            target_folder = self.get_auto_target_folder_for_images(image_folders, target_folders, folder_name)
            if target_folder is None:
                self.log_message(f"  保持 {len(image_folders)} 个图片文件夹在原位置")
                return

        # 执行文件夹移动
        for image_folder_info in image_folders:
            if self.should_stop:
                break
            self.move_image_folder(image_folder_info, target_folder)

    def get_auto_target_folder_for_images(self, image_folders, target_folders, folder_name):
        """获取图片文件夹自动处理的目标文件夹"""
        # 获取当前操作的文件夹信息
        current_operation = None
        for operation in self.preview_operations:
            if operation['folder_name'] == folder_name:
                current_operation = operation
                break

        if not current_operation:
            # 如果找不到操作信息，使用默认逻辑
            return target_folders.get('image')

        # 使用默认判断逻辑
        file_types_count = 0
        if current_operation['video_files']:
            file_types_count += 1
        if current_operation['image_folders']:
            file_types_count += 1
        if current_operation['other_files']:
            file_types_count += 1

        if file_types_count <= 1:
            # 只有一种文件类型，移动到根目录（子文件夹的根目录）
            # 根目录就是子文件夹本身
            root_folder = current_operation['folder_path']
            self.log_message(f"  默认判断：只有一种文件类型，移动 {len(image_folders)} 个图片文件夹到子文件夹根目录")
            return root_folder
        else:
            # 多种文件类型，创建分类文件夹
            target_folder = target_folders.get('image')
            self.log_message(f"  默认判断：多种文件类型，移动 {len(image_folders)} 个图片文件夹到图片文件夹")
            return target_folder

    def process_small_files_by_action(self, small_files, action):
        """根据用户操作处理小文件"""
        if not small_files:
            return

        if action == 'keep':
            self.log_message(f"  保留 {len(small_files)} 个小文件")
            return
        elif action == 'move_to_other':
            self.log_message(f"  移动 {len(small_files)} 个小文件到其他文件夹")
            # 这里需要目标文件夹，暂时跳过实现
            return
        else:  # delete
            # 统计0字节文件和小文件的数量
            zero_byte_files = [f for f in small_files if f['size'] == 0]
            small_size_files = [f for f in small_files if f['size'] > 0]

            if zero_byte_files and small_size_files:
                self.log_message(f"  删除 {len(zero_byte_files)} 个0字节文件和 {len(small_size_files)} 个小文件(<100KB)")
            elif zero_byte_files:
                self.log_message(f"  删除 {len(zero_byte_files)} 个0字节文件")
            elif small_size_files:
                self.log_message(f"  删除 {len(small_size_files)} 个小文件(<100KB)")

            for small_file in small_files:
                if self.should_stop:
                    break
                self.delete_small_file(small_file)




        
    def restore_operations(self):
        """还原之前的操作"""
        if not self.operation_log:
            messagebox.showinfo("信息", "没有可还原的操作记录！")
            return

        result = messagebox.askyesno("确认还原",
            f"确定要还原最近的 {len(self.operation_log)} 个操作吗？\n"
            "这将撤销之前的文件移动和删除操作。")

        if result:
            self.log_message("开始还原操作...")

            # 在新线程中执行还原操作
            self.is_running = True
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")

            thread = threading.Thread(target=self.perform_restore)
            thread.daemon = True
            thread.start()

    def perform_restore(self):
        """执行还原操作（在后台线程中运行）"""
        try:
            self.status_var.set("正在还原操作...")

            # 按时间倒序还原操作
            operations_to_restore = sorted(self.operation_log,
                                         key=lambda x: x['timestamp'],
                                         reverse=True)

            total_operations = len(operations_to_restore)
            completed_operations = 0

            for operation in operations_to_restore:
                if self.should_stop:
                    break

                try:
                    if operation['type'] == 'move_file':
                        self.restore_move_file(operation)
                    elif operation['type'] == 'move_folder':
                        self.restore_move_folder(operation)
                    elif operation['type'] == 'delete_file':
                        self.restore_delete_file(operation)
                    elif operation['type'] == 'delete_folder':
                        self.restore_delete_folder(operation)

                    completed_operations += 1
                    self.progress_var.set((completed_operations / total_operations) * 100)

                except Exception as e:
                    self.log_message(f"还原操作失败: {str(e)}")

            # 清空操作日志
            if not self.should_stop:
                self.operation_log.clear()
                self.save_operation_log()
                self.log_message("所有操作已还原完成！")
                self.status_var.set("还原完成")

        except Exception as e:
            self.log_message(f"还原过程中出现错误: {str(e)}")
            messagebox.showerror("错误", f"还原过程中出现错误:\n{str(e)}")

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.progress_var.set(0)

    def restore_move_file(self, operation):
        """还原文件移动操作"""
        target_path = operation['target']
        source_path = operation['source']

        if os.path.exists(target_path):
            # 确保源目录存在
            source_dir = os.path.dirname(source_path)
            if not os.path.exists(source_dir):
                os.makedirs(source_dir)

            # 移动文件回原位置
            shutil.move(target_path, source_path)
            self.log_message(f"还原文件: {operation['new_name']} -> {operation['original_name']}")
        else:
            self.log_message(f"无法还原文件 {operation['new_name']}: 文件不存在")

    def restore_move_folder(self, operation):
        """还原文件夹移动操作"""
        target_path = operation['target']
        source_path = operation['source']

        if os.path.exists(target_path):
            # 确保源目录的父目录存在
            source_parent = os.path.dirname(source_path)
            if not os.path.exists(source_parent):
                os.makedirs(source_parent)

            # 移动文件夹回原位置
            shutil.move(target_path, source_path)
            self.log_message(f"还原文件夹: {operation['new_name']} -> {operation['original_name']}")
        else:
            self.log_message(f"无法还原文件夹 {operation['new_name']}: 文件夹不存在")

    def restore_delete_file(self, operation):
        """还原文件删除操作（无法真正还原，只能记录）"""
        self.log_message(f"无法还原已删除的文件: {operation['name']} (文件已被永久删除)")

    def restore_delete_folder(self, operation):
        """还原文件夹删除操作"""
        folder_path = operation['path']

        try:
            # 重新创建被删除的空文件夹
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
                self.log_message(f"还原空文件夹: {operation['relative_path']}")
            else:
                self.log_message(f"文件夹已存在，无需还原: {operation['relative_path']}")
        except Exception as e:
            self.log_message(f"还原文件夹失败 {operation['relative_path']}: {str(e)}")
    
    def clear_log(self):
        """清空操作日志"""
        result = messagebox.askyesno("确认", "确定要清空所有操作日志吗？\n清空后将无法还原之前的操作！")
        if result:
            self.operation_log.clear()
            self.save_operation_log()
            self.log_text.delete(1.0, tk.END)
            self.log_message("操作日志已清空")
    
    def scan_subfolders(self, root_path):
        """扫描根目录下的一级子目录作为待整理单位"""
        target_folders = []

        try:
            # 获取根目录下的直接子文件夹
            for item in os.listdir(root_path):
                if self.should_stop:
                    break

                item_path = os.path.join(root_path, item)
                if os.path.isdir(item_path):
                    # 检查这个一级子目录是否包含文件（递归检查）
                    total_files = self.count_files_recursive(item_path)
                    if total_files > 0:
                        target_folders.append(item_path)
                        # 移除扫描信息输出
                        # self.log_message(f"发现需要整理的一级子目录: {item} (总共{total_files}个文件)")
                    else:
                        # 移除扫描信息输出
                        # self.log_message(f"跳过空的一级子目录: {item}")
                        pass

        except PermissionError:
            self.log_message(f"❌ 无法访问文件夹: {root_path}")
        except Exception as e:
            self.log_message(f"❌ 扫描文件夹时出错: {str(e)}")

        # 排序以确保处理顺序一致
        target_folders.sort()

        # 移除总结信息输出
        # self.log_message(f"总共发现 {len(target_folders)} 个需要整理的一级子目录")

        return target_folders

    def count_files_recursive(self, folder_path):
        """递归统计文件夹中的文件数量"""
        file_count = 0
        try:
            for root, dirs, files in os.walk(folder_path):
                if self.should_stop:
                    break
                # 过滤掉系统文件和隐藏文件
                valid_files = [f for f in files if not f.startswith('.') and not f.startswith('~') and f != 'Thumbs.db']
                file_count += len(valid_files)
        except:
            pass
        return file_count

    def scan_folder_files(self, folder_path):
        """递归扫描一级子目录下的所有文件"""
        all_files = []

        try:
            # 递归扫描一级子目录下的所有文件
            for root, dirs, files in os.walk(folder_path):
                if self.should_stop:
                    break

                # 处理当前目录下的所有文件
                for file_name in files:
                    # 过滤系统文件和隐藏文件
                    if not file_name.startswith('.') and not file_name.startswith('~') and file_name != 'Thumbs.db':
                        file_path = os.path.join(root, file_name)
                        try:
                            file_size = os.path.getsize(file_path)
                            all_files.append({
                                'path': file_path,
                                'name': file_name,
                                'size': file_size,
                                'parent_dir': root,
                                'relative_path': os.path.relpath(file_path, folder_path)
                            })
                        except (OSError, FileNotFoundError):
                            # 静默跳过无法访问的文件
                            pass
                        except Exception:
                            # 静默跳过处理出错的文件
                            pass

        except Exception as e:
            self.log_message(f"❌ 扫描一级子目录 '{os.path.basename(folder_path)}' 时出错: {str(e)}")

        return all_files

    def classify_folder_files(self, files, base_folder):
        """对一级子目录中的所有文件进行分类"""
        video_files = []
        image_files = []
        other_files = []
        small_files = []

        # 按子文件夹分组统计图片（用于识别图片文件夹）
        subfolder_images = {}

        for file_info in files:
            if self.should_stop:
                break

            file_ext = Path(file_info['name']).suffix.lower()

            # 检查文件大小
            if file_info['size'] == 0:  # 大小为0的文件，直接删除
                small_files.append(file_info)
                continue
            elif file_info['size'] < 100 * 1024:  # 小于100KB的文件
                small_files.append(file_info)
                continue

            # 分类文件
            if file_ext in self.video_extensions:
                video_files.append(file_info)
            elif file_ext in self.image_extensions:
                # 按父目录分组图片文件（用于识别图片文件夹）
                parent_dir = file_info['parent_dir']
                if parent_dir not in subfolder_images:
                    subfolder_images[parent_dir] = []
                subfolder_images[parent_dir].append(file_info)
                # 同时也加入总的图片文件列表
                image_files.append(file_info)
            else:
                other_files.append(file_info)

        # 识别图片文件夹
        image_folders = []
        min_images = self.image_settings.get('min_images_for_folder', 2)

        for subfolder_path, images in subfolder_images.items():
            if len(images) >= min_images:
                # 检查该子文件夹是否只包含图片文件（没有视频和其他文件）
                subfolder_videos = [v for v in video_files if v['parent_dir'] == subfolder_path]
                subfolder_others = [o for o in other_files if o['parent_dir'] == subfolder_path]

                if len(subfolder_videos) == 0 and len(subfolder_others) == 0:
                    # 纯图片文件夹
                    image_folders.append({
                        'folder_path': subfolder_path,
                        'image_files': images,
                        'image_count': len(images),
                        'base_folder': base_folder
                    })
                    # 不从图片文件列表中移除，保持所有图片都被统计

        return video_files, image_folders, image_files, other_files, small_files

    def create_target_folders(self, base_path, video_files, image_folders, image_files, other_files):
        """根据文件类型创建目标分类文件夹"""
        folders = {}

        # 统计文件类型数量
        file_types_count = 0
        if video_files:
            file_types_count += 1
        if image_folders:
            file_types_count += 1
        if image_files:  # 新增：单独图片文件类型统计
            file_types_count += 1
        if other_files:
            file_types_count += 1

        # 如果只有一种文件类型，不创建分类文件夹，直接使用原文件夹
        if file_types_count <= 1:
            folders = {
                'video': base_path,
                'image': base_path,
                'image_file': base_path,  # 新增：单独图片文件
                'other': base_path
            }
            self.log_message(f"  文件夹只包含单一类型文件，保持原有结构")
        else:
            # 多种文件类型，创建分类文件夹
            folders = {
                'video': os.path.join(base_path, '视频'),
                'image': os.path.join(base_path, '图片'),
                'other': os.path.join(base_path, '其他')
            }

            for folder_type, folder_path in folders.items():
                # 只创建需要的文件夹
                if folder_path != base_path and not os.path.exists(folder_path):
                    if (folder_type == 'video' and video_files) or \
                       (folder_type == 'image' and image_folders) or \
                       (folder_type == 'other' and other_files):
                        os.makedirs(folder_path)
                        try:
                            rel_path = os.path.relpath(folder_path, self.selected_folder.get())
                        except ValueError:
                            # 处理不同盘符的情况
                            rel_path = folder_path
                        self.log_message(f"创建分类文件夹: {rel_path}")

        return folders

    def process_folder_files(self, base_folder, video_files, image_folders, image_files, other_files, small_files, target_folders):
        """处理单个文件夹中的文件"""
        folder_name = os.path.basename(base_folder)

        # 判断是否为单一文件类型（不创建分类文件夹的情况）
        is_single_type = target_folders['video'] == base_folder

        # 移动视频文件
        if video_files:
            if is_single_type:
                self.log_message(f"  保持 {len(video_files)} 个视频文件在原位置")
            else:
                self.log_message(f"  移动 {len(video_files)} 个视频文件到 {folder_name}/视频")
            for video_file in video_files:
                if self.should_stop:
                    break
                self.move_file(video_file, target_folders['video'])

        # 移动图片文件夹
        if image_folders:
            if is_single_type:
                self.log_message(f"  保持 {len(image_folders)} 个图片文件夹在原位置")
            else:
                self.log_message(f"  移动 {len(image_folders)} 个图片文件夹到 {folder_name}/图片")
            for image_folder_info in image_folders:
                if self.should_stop:
                    break
                self.move_image_folder(image_folder_info, target_folders['image'])

        # 移动单独图片文件
        if image_files:
            target_folder = target_folders.get('image_file', target_folders['other'])  # 默认使用其他文件夹
            if is_single_type:
                self.log_message(f"  保持 {len(image_files)} 个图片文件在原位置")
            else:
                target_name = "图片" if target_folder == target_folders.get('image', target_folders['other']) else "其他"
                self.log_message(f"  移动 {len(image_files)} 个图片文件到 {folder_name}/{target_name}")
            for image_file in image_files:
                if self.should_stop:
                    break
                self.move_file(image_file, target_folder)

        # 移动其他文件
        if other_files:
            if is_single_type:
                self.log_message(f"  保持 {len(other_files)} 个其他文件在原位置")
            else:
                self.log_message(f"  移动 {len(other_files)} 个其他文件到 {folder_name}/其他")
            for other_file in other_files:
                if self.should_stop:
                    break
                self.move_file(other_file, target_folders['other'])

        # 删除小文件
        if small_files:
            self.log_message(f"  删除 {len(small_files)} 个小文件(<100KB)")
            for small_file in small_files:
                if self.should_stop:
                    break
                self.delete_small_file(small_file)

    def get_unique_filename(self, target_path, original_name):
        """获取唯一的文件名，避免重名冲突"""
        if not os.path.exists(os.path.join(target_path, original_name)):
            return original_name

        name_part, ext_part = os.path.splitext(original_name)
        counter = 1

        while True:
            new_name = f"{name_part}-{counter}{ext_part}"
            if not os.path.exists(os.path.join(target_path, new_name)):
                return new_name
            counter += 1

    def move_file(self, file_info, target_folder):
        """移动单个文件"""
        try:
            source_path = file_info['path']
            original_name = file_info['name']
            source_folder = os.path.dirname(source_path)

            # 如果目标文件夹与源文件夹相同，跳过移动
            if os.path.abspath(target_folder) == os.path.abspath(source_folder):
                return

            # 获取唯一文件名
            unique_name = self.get_unique_filename(target_folder, original_name)
            target_path = os.path.join(target_folder, unique_name)

            # 移动文件
            shutil.move(source_path, target_path)

            # 记录操作（增强版本，包含更多信息用于撤销）
            operation = {
                'type': 'move_file',
                'operation': 'move',  # 操作类型
                'source': source_path,
                'target': target_path,
                'source_path': source_path,  # 兼容撤销逻辑
                'target_path': target_path,  # 兼容撤销逻辑
                'original_name': original_name,
                'new_name': unique_name,
                'file_type': 'file',
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            if unique_name != original_name:
                self.log_message(f"移动文件: {original_name} -> {unique_name} (重命名)")
            else:
                self.log_message(f"移动文件: {original_name}")

        except Exception as e:
            self.log_message(f"移动文件失败 {file_info['name']}: {str(e)}")

    def move_image_folder(self, image_folder_info, target_folder):
        """移动图片文件夹"""
        try:
            source_folder = image_folder_info['folder_path']
            folder_name = os.path.basename(source_folder)
            source_parent = os.path.dirname(source_folder)

            # 如果目标文件夹与源文件夹的父目录相同，跳过移动
            if os.path.abspath(target_folder) == os.path.abspath(source_parent):
                return

            # 获取唯一文件夹名
            unique_name = self.get_unique_filename(target_folder, folder_name)
            target_path = os.path.join(target_folder, unique_name)

            # 移动整个文件夹
            shutil.move(source_folder, target_path)

            # 记录操作（增强版本，包含更多信息用于撤销）
            operation = {
                'type': 'move_folder',
                'operation': 'move',  # 操作类型
                'source': source_folder,
                'target': target_path,
                'source_path': source_folder,  # 兼容撤销逻辑
                'target_path': target_path,  # 兼容撤销逻辑
                'original_name': folder_name,
                'new_name': unique_name,
                'file_type': 'folder',
                'image_count': image_folder_info['image_count'],
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            if unique_name != folder_name:
                self.log_message(f"移动图片文件夹: {folder_name} -> {unique_name} (重命名)")
            else:
                self.log_message(f"移动图片文件夹: {folder_name} ({image_folder_info['image_count']}张图片)")

        except Exception as e:
            self.log_message(f"移动图片文件夹失败 {os.path.basename(image_folder_info['folder_path'])}: {str(e)}")

    def delete_small_file(self, file_info):
        """删除小文件"""
        try:
            file_path = file_info['path']
            file_name = file_info['name']
            file_size = file_info['size']

            # 删除文件
            os.remove(file_path)

            # 记录操作（增强版本，包含更多信息用于撤销）
            operation = {
                'type': 'delete_file',
                'operation': 'delete',  # 操作类型
                'source': file_path,
                'source_path': file_path,  # 兼容撤销逻辑
                'name': file_name,
                'size': file_size,
                'file_type': 'file',
                'timestamp': datetime.now().isoformat()
            }
            self.operation_log.append(operation)

            if file_size == 0:
                self.log_message(f"删除0字节文件: {file_name}")
            else:
                self.log_message(f"删除小文件: {file_name} ({self.format_file_size(file_size)})")

        except Exception as e:
            self.log_message(f"删除小文件失败 {file_info['name']}: {str(e)}")

    def clean_empty_folders(self, base_path, prompt_user=False):
        """清理空文件夹，包括空的分类文件夹

        Args:
            base_path: 要清理的基础路径
            prompt_user: 是否弹出确认框询问用户（True=预览时，False=自动整理时）
        """
        try:
            deleted_folders = []
            empty_folders = []

            # 从最深层开始查找空文件夹
            for root, _, _ in os.walk(base_path, topdown=False):
                # 跳过根目录
                if root == base_path:
                    continue

                # 如果文件夹为空，记录它
                try:
                    if not os.listdir(root):
                        empty_folders.append(root)
                except OSError:
                    # 无法访问文件夹
                    pass

            # 如果需要提示用户且有空文件夹
            if prompt_user and empty_folders:
                folder_names = [os.path.relpath(f, self.selected_folder.get()) for f in empty_folders]
                folder_list = '\n'.join(f"• {name}" for name in folder_names[:10])  # 最多显示10个
                if len(empty_folders) > 10:
                    folder_list += f"\n... 还有 {len(empty_folders) - 10} 个文件夹"

                result = messagebox.askyesno(
                    "发现空文件夹",
                    f"发现 {len(empty_folders)} 个空文件夹：\n\n{folder_list}\n\n是否删除这些空文件夹？",
                    icon='question'
                )

                if not result:
                    self.log_message(f"用户选择保留 {len(empty_folders)} 个空文件夹")
                    return

            # 删除空文件夹
            for root in empty_folders:
                try:
                    if os.path.exists(root) and not os.listdir(root):
                        folder_name = os.path.basename(root)
                        os.rmdir(root)
                        relative_path = os.path.relpath(root, self.selected_folder.get())
                        deleted_folders.append(relative_path)

                        # 记录删除操作
                        operation = {
                            'type': 'delete_folder',
                            'operation': 'delete',
                            'path': root,
                            'source_path': root,
                            'relative_path': relative_path,
                            'folder_name': folder_name,
                            'file_type': 'folder',
                            'is_category_folder': folder_name in ['视频', '图片', '其他'],
                            'timestamp': datetime.now().isoformat()
                        }
                        self.operation_log.append(operation)

                        # 区分显示普通文件夹和分类文件夹
                        if folder_name in ['视频', '图片', '其他']:
                            self.log_message(f"  删除空的分类文件夹: {relative_path}")
                        else:
                            self.log_message(f"  删除空文件夹: {relative_path}")

                except OSError:
                    # 文件夹不为空或无法删除
                    pass

            if deleted_folders:
                category_folders = sum(1 for f in deleted_folders if os.path.basename(f) in ['视频', '图片', '其他'])
                normal_folders = len(deleted_folders) - category_folders

                if category_folders > 0 and normal_folders > 0:
                    self.log_message(f"  清理了 {normal_folders} 个空文件夹和 {category_folders} 个空分类文件夹")
                elif category_folders > 0:
                    self.log_message(f"  清理了 {category_folders} 个空分类文件夹")
                else:
                    self.log_message(f"  清理了 {normal_folders} 个空文件夹")

        except Exception as e:
            self.log_message(f"清理空文件夹时出错: {str(e)}")

    def check_and_prompt_delete_empty_folder(self, folder_path):
        """检查文件夹是否为空，如果为空则弹出确认框询问是否删除"""
        try:
            # 检查文件夹是否存在
            if not os.path.exists(folder_path):
                return

            # 检查是否为根目录（不删除根目录）
            if folder_path == self.selected_folder.get():
                return

            # 检查文件夹是否为空
            if os.listdir(folder_path):
                return  # 文件夹不为空，不需要删除

            # 获取文件夹名称和相对路径
            folder_name = os.path.basename(folder_path)
            relative_path = os.path.relpath(folder_path, self.selected_folder.get())

            # 弹出确认对话框
            result = messagebox.askyesno(
                "删除空文件夹",
                f"文件夹 '{folder_name}' 现在为空。\n\n"
                f"路径: {relative_path}\n\n"
                f"是否删除此空文件夹？",
                icon='question'
            )

            if result:
                # 用户确认删除
                os.rmdir(folder_path)

                # 记录删除操作
                operation = {
                    'type': 'delete_folder',
                    'operation': 'delete',
                    'path': folder_path,
                    'source_path': folder_path,
                    'relative_path': relative_path,
                    'folder_name': folder_name,
                    'file_type': 'folder',
                    'is_category_folder': folder_name in ['视频', '图片', '其他'],
                    'timestamp': datetime.now().isoformat()
                }
                self.operation_log.append(operation)

                self.log_message(f"已删除空文件夹: {relative_path}")

                # 递归检查父文件夹是否也变为空
                parent_folder = os.path.dirname(folder_path)
                if parent_folder != self.selected_folder.get():
                    self.check_and_prompt_delete_empty_folder(parent_folder)
            else:
                self.log_message(f"保留空文件夹: {relative_path}")

        except Exception as e:
            self.log_message(f"检查空文件夹时出错: {str(e)}")

    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

    def show_statistics(self, video_files, image_folders, other_files, small_files):
        """显示统计信息"""
        total_video_size = sum(f['size'] for f in video_files)
        total_other_size = sum(f['size'] for f in other_files)
        total_small_size = sum(f['size'] for f in small_files)

        # 计算图片文件夹总大小
        total_image_size = 0
        for folder_info in image_folders:
            for image_file in folder_info['image_files']:
                total_image_size += image_file['size']

        self.log_message("\n统计信息:")
        self.log_message(f"  视频文件: {len(video_files)} 个, 总大小: {self.format_file_size(total_video_size)}")
        self.log_message(f"  图片文件夹: {len(image_folders)} 个, 总大小: {self.format_file_size(total_image_size)}")
        self.log_message(f"  其他文件: {len(other_files)} 个, 总大小: {self.format_file_size(total_other_size)}")
        self.log_message(f"  小文件(已删除): {len(small_files)} 个, 总大小: {self.format_file_size(total_small_size)}")

        total_size = total_video_size + total_image_size + total_other_size + total_small_size
        self.log_message(f"  总计: {len(video_files) + len(image_folders) + len(other_files) + len(small_files)} 项, 总大小: {self.format_file_size(total_size)}")

    def on_closing(self):
        """程序关闭时的处理"""
        if self.is_running:
            result = messagebox.askyesno("确认退出", "程序正在运行中，确定要退出吗？")
            if not result:
                return
            self.should_stop = True

        # 保存操作日志
        self.save_operation_log()
        self.root.destroy()

    def organize_files(self):
        """整理文件的主要逻辑（在后台线程中运行）"""
        try:
            root_path = self.selected_folder.get()

            # 第一步：扫描子文件夹
            self.status_var.set("正在扫描子文件夹...")
            self.log_message("开始扫描子文件夹...")

            subfolders = self.scan_subfolders(root_path)
            if self.should_stop:
                return

            self.log_message(f"找到 {len(subfolders)} 个子文件夹需要整理")

            if not subfolders:
                self.log_message("没有找到需要整理的子文件夹")
                self.status_var.set("没有需要整理的内容")
                return

            # 统计总体信息
            total_video_files = 0
            total_image_folders = 0
            total_image_files = 0  # 新增：单独图片文件统计
            total_other_files = 0
            total_small_files = 0

            # 第二步：逐个处理每个子文件夹
            for i, subfolder in enumerate(subfolders):
                if self.should_stop:
                    break

                folder_name = os.path.basename(subfolder)
                self.status_var.set(f"正在处理文件夹: {folder_name} ({i+1}/{len(subfolders)})")
                self.log_message(f"\n开始处理文件夹: {folder_name}")

                # 扫描当前文件夹的文件
                folder_files = self.scan_folder_files(subfolder)
                if not folder_files:
                    self.log_message(f"  文件夹 {folder_name} 为空，跳过")
                    continue

                # 分类当前文件夹的文件
                video_files, image_folders, image_files, other_files, small_files = self.classify_folder_files(folder_files, subfolder)

                # 更新统计
                total_video_files += len(video_files)
                total_image_folders += len(image_folders)
                total_image_files += len(image_files)  # 新增：单独图片文件统计
                total_other_files += len(other_files)
                total_small_files += len(small_files)

                # 如果没有需要处理的文件，跳过
                if not (video_files or image_folders or image_files or other_files or small_files):
                    self.log_message(f"  文件夹 {folder_name} 没有需要整理的文件")
                    continue

                # 在当前子文件夹中创建分类文件夹
                target_folders = self.create_target_folders(subfolder, video_files, image_folders, image_files, other_files)

                # 处理当前文件夹的文件
                self.process_folder_files(subfolder, video_files, image_folders, image_files, other_files, small_files, target_folders)

                # 清理当前文件夹的空文件夹
                self.clean_empty_folders(subfolder)

                # 更新进度
                progress = ((i + 1) / len(subfolders)) * 100
                self.progress_var.set(progress)

            # 保存操作日志
            self.save_operation_log()

            # 显示总体统计信息
            self.log_message(f"\n整理完成统计:")
            self.log_message(f"  - 处理了 {len(subfolders)} 个子文件夹")
            self.log_message(f"  - 视频文件: {total_video_files} 个")
            self.log_message(f"  - 图片文件夹: {total_image_folders} 个")
            self.log_message(f"  - 其他文件: {total_other_files} 个")
            self.log_message(f"  - 小文件(已删除): {total_small_files} 个")

            self.status_var.set("整理完成")
            self.log_message("所有文件夹整理完成！")

            # 显示完成对话框
            if not self.should_stop:
                total_items = total_video_files + total_image_folders + total_other_files + total_small_files
                messagebox.showinfo("完成",
                    f"文件整理完成！\n\n"
                    f"处理了 {len(subfolders)} 个子文件夹\n"
                    f"整理了 {total_items} 项内容\n"
                    f"操作记录已保存，可以使用'还原操作'功能撤销更改。")

        except Exception as e:
            self.log_message(f"整理过程中出现错误: {str(e)}")
            messagebox.showerror("错误", f"整理过程中出现错误:\n{str(e)}")

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.progress_var.set(0)
    
    def load_operation_log(self):
        """加载操作日志"""
        try:
            if os.path.exists(self.log_file):
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    self.operation_log = json.load(f)
                self.log_message(f"已加载 {len(self.operation_log)} 条操作记录")
        except Exception as e:
            self.log_message(f"加载操作日志失败: {str(e)}")

    def save_operation_log(self):
        """保存操作日志"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(self.operation_log, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存操作日志失败: {str(e)}")



    def get_folder_signature(self, folder_path):
        """生成文件夹特征签名"""
        try:
            files = []
            for root, _, filenames in os.walk(folder_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    try:
                        size = os.path.getsize(file_path)
                        ext = Path(filename).suffix.lower()
                        files.append((ext, size))
                    except:
                        continue

            # 按文件类型和大小范围生成特征
            video_count = sum(1 for ext, _ in files if ext in self.video_extensions)
            image_count = sum(1 for ext, _ in files if ext in self.image_extensions)
            other_count = len(files) - video_count - image_count

            total_size = sum(size for _, size in files)
            avg_size = total_size / len(files) if files else 0

            signature = {
                'video_count': video_count,
                'image_count': image_count,
                'other_count': other_count,
                'total_files': len(files),
                'avg_size_range': self.get_size_range(avg_size),
                'dominant_type': self.get_dominant_type(video_count, image_count, other_count)
            }

            return json.dumps(signature, sort_keys=True)
        except:
            return None

    def get_size_range(self, size):
        """将文件大小分类到范围"""
        if size < 1024 * 1024:  # < 1MB
            return 'small'
        elif size < 10 * 1024 * 1024:  # < 10MB
            return 'medium'
        elif size < 100 * 1024 * 1024:  # < 100MB
            return 'large'
        else:
            return 'huge'

    def get_dominant_type(self, video_count, image_count, other_count):
        """获取主要文件类型"""
        if video_count > image_count and video_count > other_count:
            return 'video'
        elif image_count > video_count and image_count > other_count:
            return 'image'
        elif other_count > 0:
            return 'other'
        else:
            return 'mixed'
    
    def show_image_settings(self):
        """显示图片设置界面"""
        # 创建设置窗口
        settings_window = tk.Toplevel(self.root)
        settings_window.title("🖼️ 图片处理设置")
        settings_window.geometry("550x700")
        settings_window.resizable(False, False)

        # 居中显示
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(settings_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🖼️ 图片处理设置", font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # 创建滚动框架
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 图片文件夹识别设置
        folder_frame = ttk.LabelFrame(scrollable_frame, text="📁 图片文件夹识别", padding="15")
        folder_frame.pack(fill=tk.X, pady=(0, 15))

        # 最小图片数量设置
        min_images_frame = ttk.Frame(folder_frame)
        min_images_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(min_images_frame, text="多少张图片以上认为是图片文件夹:").pack(side=tk.LEFT)
        min_images_var = tk.IntVar(value=self.image_settings['min_images_for_folder'])
        min_images_spinbox = ttk.Spinbox(min_images_frame, from_=1, to=20, width=5, textvariable=min_images_var)
        min_images_spinbox.pack(side=tk.RIGHT)

        ttk.Label(folder_frame, text="💡 少于此数量的图片将作为普通文件处理",
                 foreground='gray', font=('Arial', 9)).pack(anchor=tk.W, pady=(0, 5))

        # 保持文件夹结构
        preserve_var = tk.BooleanVar(value=self.image_settings['preserve_folder_structure'])
        preserve_check = ttk.Checkbutton(folder_frame, text="保持原有文件夹结构", variable=preserve_var)
        preserve_check.pack(anchor=tk.W, pady=(5, 0))

        # 按日期分组
        date_group_var = tk.BooleanVar(value=self.image_settings['group_by_date'])
        date_group_check = ttk.Checkbutton(folder_frame, text="按拍摄日期分组图片", variable=date_group_var)
        date_group_check.pack(anchor=tk.W, pady=(5, 0))

        # 图片质量设置
        quality_frame = ttk.LabelFrame(scrollable_frame, text="🎨 图片质量设置", padding="15")
        quality_frame.pack(fill=tk.X, pady=(0, 15))

        # 质量检查开关
        quality_check_var = tk.BooleanVar(value=self.image_settings['image_quality_check'])
        quality_check_checkbox = ttk.Checkbutton(quality_frame, text="启用图片质量检查", variable=quality_check_var)
        quality_check_checkbox.pack(anchor=tk.W, pady=(0, 10))

        # 最小分辨率设置
        resolution_frame = ttk.Frame(quality_frame)
        resolution_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(resolution_frame, text="最小图片分辨率 (像素):").pack(side=tk.LEFT)
        resolution_var = tk.IntVar(value=self.image_settings['min_image_resolution'])
        resolution_spinbox = ttk.Spinbox(resolution_frame, from_=50, to=2000, width=8, textvariable=resolution_var)
        resolution_spinbox.pack(side=tk.RIGHT)

        # 删除小图片选项
        delete_small_var = tk.BooleanVar(value=self.image_settings['delete_small_images'])
        delete_small_check = ttk.Checkbutton(quality_frame, text="删除分辨率过低的图片", variable=delete_small_var)
        delete_small_check.pack(anchor=tk.W, pady=(5, 0))

        ttk.Label(quality_frame, text="⚠️ 注意：删除操作不可恢复，请谨慎使用",
                 foreground='red', font=('Arial', 9)).pack(anchor=tk.W, pady=(5, 0))

        # 大图片处理设置
        large_image_frame = ttk.LabelFrame(scrollable_frame, text="📏 大图片处理", padding="15")
        large_image_frame.pack(fill=tk.X, pady=(0, 15))

        # 大图片阈值设置
        size_frame = ttk.Frame(large_image_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="单张图片超过多少MB单独处理:").pack(side=tk.LEFT)
        max_size_var = tk.DoubleVar(value=self.image_settings['max_single_image_size'] / (1024 * 1024))
        max_size_spinbox = ttk.Spinbox(size_frame, from_=1.0, to=100.0, increment=1.0, width=8, textvariable=max_size_var)
        max_size_spinbox.pack(side=tk.RIGHT)

        ttk.Label(large_image_frame, text="💡 超过此大小的单张图片将按大图片规则处理",
                 foreground='gray', font=('Arial', 9)).pack(anchor=tk.W, pady=(0, 5))

        # 处理方式设置
        action_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ 默认处理方式", padding="15")
        action_frame.pack(fill=tk.X, pady=(0, 15))

        # 图片文件夹处理方式
        folder_action_frame = ttk.Frame(action_frame)
        folder_action_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(folder_action_frame, text="图片文件夹默认处理:").pack(side=tk.LEFT)
        folder_action_var = tk.StringVar(value=self.image_settings['image_folder_action'])
        folder_action_combo = ttk.Combobox(folder_action_frame, textvariable=folder_action_var, width=15, state='readonly')
        folder_action_combo['values'] = ('keep', 'move_to_image', 'move_to_other')
        folder_action_combo.pack(side=tk.RIGHT)

        # 单张图片处理方式
        single_action_frame = ttk.Frame(action_frame)
        single_action_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(single_action_frame, text="单张图片默认处理:").pack(side=tk.LEFT)
        single_action_var = tk.StringVar(value=self.image_settings['single_image_action'])
        single_action_combo = ttk.Combobox(single_action_frame, textvariable=single_action_var, width=20, state='readonly')
        single_action_combo['values'] = ('move_to_local_image', 'move_to_image', 'keep')
        single_action_combo.pack(side=tk.RIGHT)

        # 大图片处理方式
        large_action_frame = ttk.Frame(action_frame)
        large_action_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(large_action_frame, text="大图片默认处理:").pack(side=tk.LEFT)
        large_action_var = tk.StringVar(value=self.image_settings['large_image_action'])
        large_action_combo = ttk.Combobox(large_action_frame, textvariable=large_action_var, width=15, state='readonly')
        large_action_combo['values'] = ('move_to_image', 'move_to_other', 'keep')
        large_action_combo.pack(side=tk.RIGHT)

        # 支持的图片格式设置
        format_frame = ttk.LabelFrame(scrollable_frame, text="🎭 支持的图片格式", padding="15")
        format_frame.pack(fill=tk.X, pady=(0, 15))

        # 当前支持的格式
        current_formats = ', '.join(sorted(ext.replace('.', '') for ext in self.image_extensions))
        format_label = ttk.Label(format_frame, text=f"当前支持: {current_formats}",
                               font=('Arial', 9), wraplength=450)
        format_label.pack(anchor=tk.W, pady=(0, 10))

        # 添加新格式
        add_format_frame = ttk.Frame(format_frame)
        add_format_frame.pack(fill=tk.X)

        ttk.Label(add_format_frame, text="添加新格式:").pack(side=tk.LEFT)
        new_format_var = tk.StringVar()
        new_format_entry = ttk.Entry(add_format_frame, textvariable=new_format_var, width=10)
        new_format_entry.pack(side=tk.LEFT, padx=(5, 5))

        def add_format():
            new_format = new_format_var.get().strip().lower()
            if new_format:
                if not new_format.startswith('.'):
                    new_format = '.' + new_format
                if new_format not in self.image_extensions:
                    self.image_extensions.add(new_format)
                    # 更新显示
                    updated_formats = ', '.join(sorted(ext.replace('.', '') for ext in self.image_extensions))
                    format_label.config(text=f"当前支持: {updated_formats}")
                    new_format_var.set('')

        ttk.Button(add_format_frame, text="添加", command=add_format).pack(side=tk.LEFT)

        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        def save_settings():
            """保存设置"""
            self.image_settings['min_images_for_folder'] = min_images_var.get()
            self.image_settings['max_single_image_size'] = int(max_size_var.get() * 1024 * 1024)
            self.image_settings['image_folder_action'] = folder_action_var.get()
            self.image_settings['single_image_action'] = single_action_var.get()
            self.image_settings['large_image_action'] = large_action_var.get()
            self.image_settings['image_quality_check'] = quality_check_var.get()
            self.image_settings['min_image_resolution'] = resolution_var.get()
            self.image_settings['delete_small_images'] = delete_small_var.get()
            self.image_settings['group_by_date'] = date_group_var.get()
            self.image_settings['preserve_folder_structure'] = preserve_var.get()

            # 保存到文件
            self.save_image_settings()

            messagebox.showinfo("设置已保存", "图片处理设置已保存！\n重新预览时将应用新设置。")
            settings_window.destroy()

        def reset_settings():
            """重置为默认设置"""
            result = messagebox.askyesno("确认重置", "确定要重置为默认设置吗？")
            if result:
                # 重置为默认值
                min_images_var.set(2)
                max_size_var.set(10.0)
                folder_action_var.set('keep')
                single_action_var.set('move_to_local_image')
                large_action_var.set('move_to_image')
                quality_check_var.set(True)
                resolution_var.set(100)
                delete_small_var.set(False)
                date_group_var.set(False)
                preserve_var.set(True)

                # 重置图片格式
                self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.ico', '.raw', '.svg'}
                updated_formats = ', '.join(sorted(ext.replace('.', '') for ext in self.image_extensions))
                format_label.config(text=f"当前支持: {updated_formats}")

        ttk.Button(button_frame, text="💾 保存设置", command=save_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔄 重置默认", command=reset_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ 取消", command=settings_window.destroy).pack(side=tk.RIGHT)

        # 居中显示窗口
        settings_window.update_idletasks()
        x = (settings_window.winfo_screenwidth() // 2) - (settings_window.winfo_width() // 2)
        y = (settings_window.winfo_screenheight() // 2) - (settings_window.winfo_height() // 2)
        settings_window.geometry(f"+{x}+{y}")

    def save_image_settings(self):
        """保存图片设置到文件"""
        try:
            settings_file = "image_settings.json"
            settings_data = {
                'image_settings': self.image_settings,
                'image_extensions': list(self.image_extensions)
            }
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)
            self.log_message("图片设置已保存")
        except Exception as e:
            self.log_message(f"保存图片设置失败: {str(e)}")

    def load_image_settings(self):
        """加载图片设置"""
        try:
            settings_file = "image_settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)

                if 'image_settings' in settings_data:
                    self.image_settings.update(settings_data['image_settings'])

                if 'image_extensions' in settings_data:
                    self.image_extensions = set(settings_data['image_extensions'])

                self.log_message("图片设置已加载")
        except Exception as e:
            self.log_message(f"加载图片设置失败: {str(e)}")

    def run(self):
        """运行程序"""
        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 居中显示窗口
        self.center_window()

        # 加载图片设置
        self.load_image_settings()

        self.root.mainloop()

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def render_preview_tree(self, parent_item, folder_tree, current_path, user_actions):
        """渲染预览文件夹树结构"""
        # 先添加当前级别的文件夹
        if 'folders' in folder_tree:
            for folder_name, sub_tree in folder_tree['folders'].items():
                # 统计子文件夹内容
                stats = self.calculate_folder_stats(sub_tree)

                # 生成标注
                annotations = []
                if stats['video_count'] > 0:
                    annotations.append(f"📹{stats['video_count']}")
                if stats['image_count'] > 0:
                    annotations.append(f"🖼️{stats['image_count']}")
                if stats['other_count'] > 0:
                    annotations.append(f"📄{stats['other_count']}")
                if stats['small_count'] > 0:
                    # 检查小文件的操作
                    small_action = user_actions.get('small_action', 'delete')
                    if small_action != 'delete':
                        annotations.append(f"🗑️{stats['small_count']}")

                annotation_text = f" [{', '.join(annotations)}]" if annotations else ""
                folder_node = self.preview_tree.insert(parent_item, 'end',
                                                      text=f"📂 {folder_name}{annotation_text}", open=False)

                # 递归渲染子文件夹
                sub_path = os.path.join(current_path, folder_name) if current_path else folder_name
                self.render_preview_tree(folder_node, sub_tree, sub_path, user_actions)

        # 然后添加当前级别的文件和文件夹
        if 'items' in folder_tree:
            items_to_show = folder_tree['items']  # 显示所有项目

            for item in items_to_show:
                # 检查文件的操作
                action = item.get('action', 'auto')

                if item['type'] == 'file':
                    file_name = item['data']['name']
                    file_size = self.format_file_size(item['data']['size'])

                    # 根据操作决定显示方式
                    if item['subtype'] == 'small' and action == 'delete':
                        # 小文件将被删除，用特殊样式显示
                        self.preview_tree.insert(parent_item, 'end', text=f"🗑️ {file_name} ({file_size}) [将删除]")
                    elif item['subtype'] == 'video':
                        self.preview_tree.insert(parent_item, 'end', text=f"🎬 {file_name} ({file_size})")
                    elif item['subtype'] == 'image_file':
                        self.preview_tree.insert(parent_item, 'end', text=f"🖼️ {file_name} ({file_size})")
                    elif item['subtype'] == 'other':
                        self.preview_tree.insert(parent_item, 'end', text=f"📄 {file_name} ({file_size})")
                    elif item['subtype'] == 'small':
                        self.preview_tree.insert(parent_item, 'end', text=f"📄 {file_name} ({file_size})")

                elif item['type'] == 'folder' and item['subtype'] == 'image_folder':
                    folder_name = os.path.basename(item['path'])
                    image_count = item['data']['image_count']
                    img_folder_node = self.preview_tree.insert(parent_item, 'end',
                                                             text=f"📂 {folder_name} [🖼️{image_count}]", open=False)

                    # 显示部分图片
                    for img_file in item['data']['image_files'][:5]:
                        img_name = img_file['name']
                        img_size = self.format_file_size(img_file['size'])
                        self.preview_tree.insert(img_folder_node, 'end', text=f"🖼️ {img_name} ({img_size})")

                    if len(item['data']['image_files']) > 5:
                        self.preview_tree.insert(img_folder_node, 'end',
                                               text=f"... 还有 {len(item['data']['image_files']) - 5} 张图片")



    def apply_and_organize_current_folder(self):
        """应用当前文件夹的设置并开始整理"""
        if not self.preview_operations:
            return

        current_operation = self.preview_operations[self.current_preview_index]
        folder_name = current_operation['folder_name']

        # 确认对话框
        result = messagebox.askyesno("确认整理",
            f"确定要按照当前设置整理文件夹 '{folder_name}' 吗？\n\n"
            "此操作将：\n"
            "1. 按照预览设置移动/删除文件\n"
            "2. 保存当前设置为用户偏好\n"
            "3. 所有操作都会被记录，可以还原")

        if result:
            # 保存用户偏好
            self.save_single_folder_preferences(current_operation)

            # 开始整理当前文件夹（保留在预览界面）
            self.organize_single_folder(current_operation)

    def undo_current_folder_changes(self):
        """撤销当前文件夹的更改"""
        if not self.preview_operations:
            return

        current_operation = self.preview_operations[self.current_preview_index]
        folder_path = current_operation['folder_path']
        folder_name = current_operation['folder_name']

        # 恢复到原始设置
        if folder_path in self.original_actions:
            current_operation['user_actions'] = self.original_actions[folder_path].copy()

            # 清除已整理状态
            if 'is_organized' in current_operation:
                del current_operation['is_organized']

            # 重新创建预览窗口以反映更改
            self.create_single_folder_preview()

            # 显示确认信息
            messagebox.showinfo("更改已撤销",
                               f"已撤销文件夹 '{folder_name}' 的所有更改，\n"
                               "恢复为智能建议设置。")

            self.log_message(f"已撤销文件夹 '{folder_name}' 的更改")
        else:
            messagebox.showwarning("无法撤销",
                                 f"无法找到文件夹 '{folder_name}' 的原始设置。")

    def undo_current_folder_operations(self):
        """撤销当前文件夹的实际文件操作"""
        if not self.preview_operations:
            return

        current_operation = self.preview_operations[self.current_preview_index]
        folder_name = current_operation['folder_name']
        folder_path = current_operation['folder_path']

        # 确认对话框
        result = messagebox.askyesno("确认撤销整理",
            f"确定要撤销文件夹 '{folder_name}' 的整理操作吗？\n\n"
            "此操作将：\n"
            "1. 还原所有被移动的文件到原位置\n"
            "2. 恢复所有被删除的文件\n"
            "3. 删除创建的分类文件夹（如果为空）\n"
            "4. 重置设置为智能建议\n\n"
            "注意：只能撤销本次会话中的操作")

        if result:
            try:
                # 查找该文件夹相关的操作记录
                folder_operations = self.find_folder_operations(folder_path)

                if not folder_operations:
                    messagebox.showwarning("无法撤销",
                                         f"未找到文件夹 '{folder_name}' 的操作记录。\n"
                                         "可能该文件夹不是在本次会话中整理的。")
                    return

                # 在后台线程中执行撤销操作
                self.perform_undo_operations(folder_operations, current_operation)

            except Exception as e:
                self.log_message(f"撤销操作时出错: {str(e)}")
                messagebox.showerror("错误", f"撤销操作时出错:\n{str(e)}")

    def find_folder_operations(self, folder_path):
        """查找指定文件夹的操作记录"""
        folder_operations = []

        # 遍历操作日志，查找相关的操作
        for operation in self.operation_log:
            if operation.get('folder_path') == folder_path:
                folder_operations.append(operation)
            elif operation.get('source_path', '').startswith(folder_path):
                folder_operations.append(operation)

        # 按时间倒序排列，以便正确撤销
        folder_operations.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        return folder_operations

    def perform_undo_operations(self, operations, current_operation):
        """执行撤销操作"""
        try:
            folder_name = current_operation['folder_name']
            self.log_message(f"开始撤销文件夹 '{folder_name}' 的操作...")

            # 设置状态
            self.is_running = True
            self.should_stop = False
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.status_var.set(f"正在撤销: {folder_name}")

            # 在新线程中执行撤销
            thread = threading.Thread(target=self._perform_undo_thread,
                                     args=(operations, current_operation))
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"启动撤销操作时出错: {str(e)}")
            messagebox.showerror("错误", f"启动撤销操作时出错:\n{str(e)}")

    def _perform_undo_thread(self, operations, current_operation):
        """在后台线程中执行撤销操作"""
        try:
            folder_name = current_operation['folder_name']
            undo_count = 0

            for operation in operations:
                if self.should_stop:
                    break

                success = self.undo_single_operation(operation)
                if success:
                    undo_count += 1

            # 清除已整理状态
            if 'is_organized' in current_operation:
                del current_operation['is_organized']

            # 恢复到原始设置
            folder_path = current_operation['folder_path']
            if folder_path in self.original_actions:
                current_operation['user_actions'] = self.original_actions[folder_path].copy()

            # 从操作日志中移除已撤销的操作
            for operation in operations:
                if operation in self.operation_log:
                    self.operation_log.remove(operation)

            # 保存更新后的操作日志
            self.save_operation_log()

            self.status_var.set("撤销完成")
            self.log_message(f"文件夹 '{folder_name}' 撤销完成！共撤销 {undo_count} 个操作")

            if not self.should_stop:
                # 在主线程中更新界面和显示提示
                self.root.after(0, lambda: self.on_undo_completed(folder_name, undo_count))

        except Exception as e:
            self.log_message(f"撤销过程中出现错误: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("错误", f"撤销过程中出现错误:\n{str(e)}"))

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.status_var.set("就绪")

    def undo_single_operation(self, operation):
        """撤销单个文件操作"""
        try:
            operation_type = operation.get('operation')
            source_path = operation.get('source_path')
            target_path = operation.get('target_path')

            if operation_type == 'move':
                # 撤销移动：将文件从目标位置移回源位置
                if os.path.exists(target_path):
                    # 确保源目录存在
                    source_dir = os.path.dirname(source_path)
                    if not os.path.exists(source_dir):
                        os.makedirs(source_dir)

                    shutil.move(target_path, source_path)
                    self.log_message(f"  撤销移动: {os.path.basename(target_path)} → 原位置")
                    return True
                else:
                    self.log_message(f"  无法撤销移动: 目标文件不存在 {target_path}")
                    return False

            elif operation_type == 'delete':
                # 删除操作无法撤销（文件已被永久删除）
                self.log_message(f"  无法撤销删除: {os.path.basename(source_path)} (文件已被永久删除)")
                return False

            elif operation_type == 'create_folder':
                # 撤销文件夹创建：删除空文件夹
                if os.path.exists(target_path) and os.path.isdir(target_path):
                    try:
                        os.rmdir(target_path)  # 只能删除空文件夹
                        self.log_message(f"  撤销文件夹创建: {os.path.basename(target_path)}")
                        return True
                    except OSError:
                        self.log_message(f"  无法删除文件夹: {target_path} (文件夹不为空)")
                        return False
                return False

            return False

        except Exception as e:
            self.log_message(f"  撤销操作失败: {str(e)}")
            return False

    def on_undo_completed(self, folder_name, undo_count):
        """撤销完成后的处理"""
        # 更新预览窗口标题
        if self.preview_window:
            current_title = self.preview_window.title()
            new_title = current_title.replace(" ✅ 已整理", "")
            self.preview_window.title(new_title)

        # 重新创建预览窗口以反映更改
        self.create_single_folder_preview()

        # 显示完成提示
        if undo_count > 0:
            messagebox.showinfo("撤销完成",
                               f"文件夹 '{folder_name}' 撤销完成！\n\n"
                               f"✅ 成功撤销 {undo_count} 个操作\n"
                               f"📁 文件已恢复到原始位置\n"
                               f"⚙️ 设置已重置为智能建议")
        else:
            messagebox.showwarning("撤销完成",
                                 f"文件夹 '{folder_name}' 没有可撤销的操作。\n"
                                 "可能所有操作都无法撤销（如删除操作）。")

    def save_single_folder_preferences(self, operation):
        """保存单个文件夹的用户偏好"""
        folder_path = operation['folder_path']
        suggested_actions = operation['suggested_actions']
        user_actions = operation['user_actions']

        # 检查用户是否修改了建议
        if suggested_actions != user_actions:
            signature = self.get_folder_signature(folder_path)
            if signature:
                self.user_preferences[signature] = user_actions.copy()
                self.save_user_preferences()

                # 记录保存的偏好设置
                folder_name = operation['folder_name']
                self.log_message(f"✅ 已保存文件夹 '{folder_name}' 的偏好设置:")
                for key, value in user_actions.items():
                    if key.endswith('_action'):
                        action_type = key.replace('_action', '')
                        self.log_message(f"  {action_type}: {value}")
                self.log_message("下次遇到相似文件夹结构时将自动应用这些设置")

                return True
        return False

    def save_user_preferences(self):
        """保存用户偏好设置到文件"""
        try:
            preferences_file = os.path.join(os.path.dirname(__file__), 'user_preferences.json')
            import json
            with open(preferences_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_preferences, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存用户偏好失败: {str(e)}")

    def load_user_preferences(self):
        """从文件加载用户偏好设置"""
        try:
            preferences_file = os.path.join(os.path.dirname(__file__), 'user_preferences.json')
            if os.path.exists(preferences_file):
                import json
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    self.user_preferences = json.load(f)
        except Exception as e:
            self.log_message(f"加载用户偏好失败: {str(e)}")
            self.user_preferences = {}

    def apply_user_preferences(self, folder_path, default_actions):
        """应用用户偏好设置到默认操作"""
        try:
            # 生成文件夹特征签名
            signature = self.get_folder_signature(folder_path)
            if not signature:
                self.log_message("无法生成文件夹签名，使用默认设置")
                return default_actions

            # 查找匹配的用户偏好
            if signature in self.user_preferences:
                saved_preferences = self.user_preferences[signature]
                self.log_message(f"找到匹配的用户偏好设置，自动应用之前的设置")

                # 应用保存的偏好，但保留默认值作为备选
                applied_actions = default_actions.copy()
                for key, value in saved_preferences.items():
                    if key in applied_actions:
                        applied_actions[key] = value

                # 记录应用的偏好
                self.log_message(f"  📹 视频文件: {applied_actions.get('video_action', 'auto')}")
                self.log_message(f"  🖼️ 图片文件夹: {applied_actions.get('image_action', 'auto')}")
                self.log_message(f"  🖼️ 图片文件: {applied_actions.get('image_file_action', 'auto')}")
                self.log_message(f"  📄 其他文件: {applied_actions.get('other_action', 'auto')}")
                self.log_message(f"  🗑️ 小文件: {applied_actions.get('small_action', 'auto')}")

                return applied_actions
            else:
                self.log_message("未找到匹配的用户偏好，使用默认设置")
                return default_actions

        except Exception as e:
            self.log_message(f"应用用户偏好时出错: {str(e)}")
            return default_actions

    def organize_single_folder(self, operation):
        """整理单个文件夹"""
        try:
            folder_name = operation['folder_name']
            self.log_message(f"开始整理文件夹: {folder_name}")

            # 设置状态
            self.is_running = True
            self.should_stop = False
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.status_var.set(f"正在整理: {folder_name}")

            # 在新线程中执行整理操作
            thread = threading.Thread(target=self._organize_single_folder_thread, args=(operation,))
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"整理文件夹时出错: {str(e)}")
            messagebox.showerror("错误", f"整理文件夹时出错:\n{str(e)}")

    def _organize_single_folder_thread(self, operation):
        """在后台线程中整理单个文件夹"""
        try:
            folder_name = operation['folder_name']

            # 根据用户设置处理文件
            self.process_folder_with_user_actions(operation)

            # 保存操作日志
            self.save_operation_log()

            self.status_var.set("整理完成")
            self.log_message(f"文件夹 '{folder_name}' 整理完成！")

            # 标记当前文件夹为已完成
            operation['is_organized'] = True

            if not self.should_stop:
                # 在主线程中更新预览界面和显示提示
                self.root.after(0, lambda: self.on_folder_organized(folder_name))

        except Exception as e:
            self.log_message(f"整理过程中出现错误: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("错误", f"整理过程中出现错误:\n{str(e)}"))

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.status_var.set("就绪")

    def on_folder_organized(self, folder_name):
        """文件夹整理完成后的处理"""
        # 更新预览窗口标题，显示已完成状态
        if self.preview_window:
            current_title = self.preview_window.title()
            if "✅" not in current_title:
                new_title = current_title.replace("手动整理", "手动整理 ✅ 已整理")
                self.preview_window.title(new_title)

        # 显示完成提示（非阻塞）
        messagebox.showinfo("完成", f"文件夹 '{folder_name}' 整理完成！\n\n"
                                   "✅ 所有操作已记录，可使用'还原操作'功能撤销\n"
                                   "📁 可以继续处理其他文件夹")

        # 刷新目录结构以反映实际的文件结构变化
        self.log_message("🔄 刷新目录结构以反映整理结果...")
        self.refresh_folder_structure()

    # ==================== 文件名规范化功能 ====================

    def normalize_filename_length(self, name, root_path, min_length=None, max_length=None):
        """
        规范化文件名长度，保持在指定范围内
        如果超出最大长度，优先在中文标点处截断，不保留标点
        处理重名情况，同时处理已有的数字后缀
        """
        if min_length is None:
            min_length = self.normalize_settings.get('min_length', 25)
        if max_length is None:
            max_length = self.normalize_settings.get('max_length', 42)

        # 分离文件名和扩展名
        base_name = name
        extension = ''
        if '.' in name and not name.startswith('.'):
            last_dot_index = name.rfind('.')
            potential_ext = name[last_dot_index:]

            # 如果扩展名长度合理，则认为是真正的扩展名
            if len(potential_ext) <= 6:
                base_name = name[:last_dot_index]
                extension = potential_ext

        # 检查是否已有数字后缀
        suffix_pattern = r'(-\d+)(?=[^-\d]|$)'
        suffix_match = None
        if base_name:
            all_suffixes = list(re.finditer(suffix_pattern, base_name))
            if all_suffixes:
                suffix_match = all_suffixes[-1]

        # 提取基本名称和数字后缀
        number_suffix = ''
        if suffix_match:
            number_suffix = suffix_match.group(1)
            base_name = base_name[:suffix_match.start()] + base_name[suffix_match.end():]

        # 检查总长度是否在范围内
        if min_length <= len(base_name) <= max_length:
            return name

        # 如果超出最大长度，需要截断
        if len(base_name) > max_length:
            if self.normalize_settings.get('smart_truncate', True):
                # 中文标点符号列表
                chinese_punctuation = '，。！？；：、''""（）【】《》〈〉「」『』〔〕…—～'

                # 在最大长度范围内查找最后一个中文标点
                truncate_pos = -1
                for i in range(max_length - 1, -1, -1):
                    if i < len(base_name) and base_name[i] in chinese_punctuation:
                        truncate_pos = i
                        break

                # 如果找到合适的截断点，使用它；否则强制截断
                if truncate_pos != -1:
                    base_name = base_name[:truncate_pos]
                else:
                    base_name = base_name[:max_length]
            else:
                base_name = base_name[:max_length]

            # 处理重名情况
            return self.handle_duplicate_filename(root_path, base_name, extension)

        # 如果长度小于最小长度，保持原样
        return name

    def handle_duplicate_filename(self, path, name, extension=''):
        """
        处理重名文件，通过添加数字后缀来避免冲突
        返回不冲突的新文件名
        """
        # 检查是否已有数字后缀
        suffix_pattern = r'(-\d+)$'
        suffix_match = re.search(suffix_pattern, name)

        if suffix_match:
            base_name = name[:suffix_match.start()]
            current_number = int(suffix_match.group(1)[1:])
            counter = current_number
        else:
            base_name = name
            counter = 1

        full_name = name + extension
        full_path = os.path.join(path, full_name)

        # 如果文件名不存在，直接返回原名
        if not os.path.exists(full_path):
            return full_name

        # 如果存在重名，则添加或增加数字后缀
        while True:
            new_name = f"{base_name}-{counter}{extension}"
            if not os.path.exists(os.path.join(path, new_name)):
                return new_name
            counter += 1

    def load_normalization_rules(self):
        """加载文件名规范化规则"""
        replacements = []
        deletions = []

        try:
            replacements_file = self.normalize_settings.get('replacements_file', 'replacements.csv')
            if os.path.exists(replacements_file):
                with open(replacements_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析替换规则
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if ',' in line:
                            parts = line.split(',', 1)  # 只分割第一个逗号
                            if len(parts) >= 2:
                                old_text = parts[0].strip()
                                new_text = parts[1].strip()
                                if old_text:  # 确保原文本不为空
                                    replacements.append([old_text, new_text])
                        else:
                            # 没有逗号的行作为删除处理
                            if line:
                                replacements.append([line, ''])
        except Exception as e:
            self.log_message(f"❌ 加载替换规则失败: {str(e)}")

        try:
            deletions_file = self.normalize_settings.get('deletions_file', 'deletions.csv')
            if os.path.exists(deletions_file):
                with open(deletions_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 解析删除规则
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        deletions.append([line])
        except Exception as e:
            self.log_message(f"❌ 加载删除规则失败: {str(e)}")

        return replacements, deletions

    def apply_normalization_rules(self, filename, replacements, deletions):
        """应用文件名规范化规则"""
        new_filename = filename

        # 应用删除规则
        for deletion_rule in deletions:
            if deletion_rule and deletion_rule[0] in new_filename:
                new_filename = new_filename.replace(deletion_rule[0], "")

        # 应用替换规则
        for old_str, new_str in replacements:
            if old_str in new_filename:
                new_filename = new_filename.replace(old_str, new_str)

        return new_filename

    def normalize_single_file(self, file_path, replacements, deletions):
        """规范化单个文件名"""
        if not os.path.exists(file_path):
            return False

        directory = os.path.dirname(file_path)
        filename = os.path.basename(file_path)

        # 应用规范化规则
        new_filename = self.apply_normalization_rules(filename, replacements, deletions)

        # 规范化长度
        new_filename = self.normalize_filename_length(new_filename, directory)

        # 如果文件名没有变化，返回False
        if new_filename == filename:
            return False

        new_file_path = os.path.join(directory, new_filename)

        try:
            os.rename(file_path, new_file_path)
            self.log_message(f"✅ 重命名: {filename} → {new_filename}")
            return True
        except Exception as e:
            self.log_message(f"❌ 重命名失败: {filename} - {str(e)}")
            return False

    def normalize_single_file_with_options(self, file_path, replacements, deletions):
        """根据用户选项规范化单个文件名"""
        if not os.path.exists(file_path):
            return False

        directory = os.path.dirname(file_path)
        filename = os.path.basename(file_path)
        original_filename = filename

        # 获取用户设置
        use_length = getattr(self, 'norm_length_var', None)

        # 应用规范化规则（如果有的话）
        if replacements or deletions:
            filename = self.apply_normalization_rules(filename, replacements, deletions)

        # 规范化长度（如果启用）
        if use_length and use_length.get():
            # 获取用户设置的长度范围
            try:
                min_length = int(getattr(self, 'norm_min_var', tk.StringVar(value="25")).get())
                max_length = int(getattr(self, 'norm_max_var', tk.StringVar(value="42")).get())
            except:
                min_length = 25
                max_length = 42

            filename = self.normalize_filename_length(filename, directory, min_length, max_length)

        # 如果文件名没有变化，返回False
        if filename == original_filename:
            return False

        new_file_path = os.path.join(directory, filename)

        try:
            os.rename(file_path, new_file_path)
            self.log_message(f"✅ 重命名: {original_filename} → {filename}")
            return True
        except Exception as e:
            self.log_message(f"❌ 重命名失败: {original_filename} - {str(e)}")
            return False

    def normalize_single_file_with_options(self, file_path, replacements, deletions, folder_index=0):
        """根据用户选项规范化单个文件名"""
        if not os.path.exists(file_path):
            return False

        directory = os.path.dirname(file_path)
        filename = os.path.basename(file_path)
        original_filename = filename

        folder_id = f"folder_{folder_index}"

        # 获取用户设置
        use_length = getattr(self, f'norm_length_var_{folder_id}', None)

        # 应用规范化规则（如果有的话）
        if replacements or deletions:
            filename = self.apply_normalization_rules(filename, replacements, deletions)

        # 规范化长度（如果启用）
        if use_length and use_length.get():
            # 获取用户设置的长度范围
            try:
                min_length = int(getattr(self, f'norm_min_var_{folder_id}', tk.StringVar(value="25")).get())
                max_length = int(getattr(self, f'norm_max_var_{folder_id}', tk.StringVar(value="42")).get())
            except:
                min_length = 25
                max_length = 42

            filename = self.normalize_filename_length(filename, directory, min_length, max_length)

        # 如果文件名没有变化，返回False
        if filename == original_filename:
            return False

        new_file_path = os.path.join(directory, filename)

        try:
            os.rename(file_path, new_file_path)
            self.log_message(f"✅ 重命名: {original_filename} → {filename}")
            return True
        except Exception as e:
            self.log_message(f"❌ 重命名失败: {original_filename} - {str(e)}")
            return False

    def get_folder_index_by_name(self, folder_name):
        """根据文件夹名称获取索引"""
        for i, operation in enumerate(self.preview_operations):
            if operation['folder_name'] == folder_name:
                return i
        return 0  # 默认返回0

    def normalize_files_list(self, files, folder_index=0):
        """规范化文件列表中的文件名"""
        folder_id = f"folder_{folder_index}"

        # 获取用户设置
        use_replacements = getattr(self, f'norm_replace_var_{folder_id}', None)
        use_deletions = getattr(self, f'norm_delete_var_{folder_id}', None)
        use_length = getattr(self, f'norm_length_var_{folder_id}', None)

        # 加载规范化规则
        replacements, deletions = self.load_normalization_rules()

        # 根据用户设置决定是否使用规则
        if use_replacements and not use_replacements.get():
            replacements = []
        if use_deletions and not use_deletions.get():
            deletions = []

        if not replacements and not deletions and (not use_length or not use_length.get()):
            self.log_message("⚠️ 未启用任何规范化选项，跳过处理")
            return 0

        normalized_count = 0

        for file_info in files:
            if self.should_stop:
                break

            file_path = file_info['path']
            if self.normalize_single_file_with_options(file_path, replacements, deletions, folder_index):
                normalized_count += 1

        return normalized_count

    def normalize_folder_files(self, folder_path):
        """批量规范化文件夹中的所有文件名"""
        if not self.normalize_settings.get('enable_normalization', False):
            self.log_message("⚠️ 文件名规范化功能未启用")
            return 0

        self.log_message(f"🔄 开始规范化文件夹: {folder_path}")

        # 加载规范化规则
        replacements, deletions = self.load_normalization_rules()

        if not replacements and not deletions:
            self.log_message("⚠️ 未找到规范化规则，跳过处理")
            return 0

        normalized_count = 0

        try:
            # 递归处理所有文件
            for root, dirs, files in os.walk(folder_path):
                if self.should_stop:
                    break

                # 先处理文件
                for filename in files:
                    if self.should_stop:
                        break

                    file_path = os.path.join(root, filename)
                    if self.normalize_single_file(file_path, replacements, deletions):
                        normalized_count += 1

                # 再处理文件夹名
                for dirname in dirs[:]:  # 使用切片复制，避免修改时出错
                    if self.should_stop:
                        break

                    dir_path = os.path.join(root, dirname)
                    new_dirname = self.apply_normalization_rules(dirname, replacements, deletions)
                    new_dirname = self.normalize_filename_length(new_dirname, root)

                    if new_dirname != dirname:
                        new_dir_path = os.path.join(root, new_dirname)
                        try:
                            os.rename(dir_path, new_dir_path)
                            self.log_message(f"✅ 重命名文件夹: {dirname} → {new_dirname}")
                            normalized_count += 1
                            # 更新dirs列表中的名称
                            dirs[dirs.index(dirname)] = new_dirname
                        except Exception as e:
                            self.log_message(f"❌ 重命名文件夹失败: {dirname} - {str(e)}")

        except Exception as e:
            self.log_message(f"❌ 批量规范化时出错: {str(e)}")

        self.log_message(f"✅ 规范化完成，共处理 {normalized_count} 个文件/文件夹")
        return normalized_count







    def create_normalization_settings_for_folder(self, parent, operation, index):
        """为单个文件夹创建规范化设置区域"""
        # 规范化设置框架
        norm_frame = ttk.LabelFrame(parent, text="📝 文件名规范化设置", padding="5")
        norm_frame.pack(fill=tk.X, pady=(10, 0))

        # 为每个文件夹创建独立的变量
        folder_id = f"folder_{index}"

        # 第一行：主要开关
        row1 = ttk.Frame(norm_frame)
        row1.pack(fill=tk.X, pady=(0, 5))

        # 启用规范化
        enable_var = tk.BooleanVar(value=self.normalize_settings.get('enable_normalization', True))
        setattr(self, f'norm_enable_var_{folder_id}', enable_var)
        enable_check = ttk.Checkbutton(row1, text="启用规范化", variable=enable_var)
        enable_check.pack(side=tk.LEFT, padx=(0, 15))

        # 应用替换规则
        replace_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_replace_var_{folder_id}', replace_var)
        replace_check = ttk.Checkbutton(row1, text="替换规则", variable=replace_var)
        replace_check.pack(side=tk.LEFT, padx=(0, 15))

        # 应用删除规则
        delete_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_delete_var_{folder_id}', delete_var)
        delete_check = ttk.Checkbutton(row1, text="删除规则", variable=delete_var)
        delete_check.pack(side=tk.LEFT, padx=(0, 15))

        # 第二行：长度设置
        row2 = ttk.Frame(norm_frame)
        row2.pack(fill=tk.X, pady=(0, 5))

        # 长度规范化
        length_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_length_var_{folder_id}', length_var)
        length_check = ttk.Checkbutton(row2, text="长度规范化", variable=length_var)
        length_check.pack(side=tk.LEFT, padx=(0, 10))

        # 长度范围
        ttk.Label(row2, text="范围:").pack(side=tk.LEFT, padx=(0, 2))
        min_var = tk.StringVar(value=str(self.normalize_settings.get('min_length', 25)))
        setattr(self, f'norm_min_var_{folder_id}', min_var)
        min_entry = ttk.Entry(row2, textvariable=min_var, width=3)
        min_entry.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(row2, text="-").pack(side=tk.LEFT, padx=(0, 2))
        max_var = tk.StringVar(value=str(self.normalize_settings.get('max_length', 42)))
        setattr(self, f'norm_max_var_{folder_id}', max_var)
        max_entry = ttk.Entry(row2, textvariable=max_var, width=3)
        max_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 智能截断
        smart_var = tk.BooleanVar(value=self.normalize_settings.get('smart_truncate', True))
        setattr(self, f'norm_smart_var_{folder_id}', smart_var)
        smart_check = ttk.Checkbutton(row2, text="智能截断", variable=smart_var)
        smart_check.pack(side=tk.LEFT, padx=(0, 10))

        # 处理重名
        duplicate_var = tk.BooleanVar(value=self.normalize_settings.get('auto_handle_duplicates', True))
        setattr(self, f'norm_duplicate_var_{folder_id}', duplicate_var)
        duplicate_check = ttk.Checkbutton(row2, text="处理重名", variable=duplicate_var)
        duplicate_check.pack(side=tk.LEFT)

    def create_independent_normalization_settings(self, parent, operation, index):
        """创建独立的文件名规范化设置区域"""
        # 为每个文件夹创建独立的变量
        folder_id = f"folder_{index}"

        # 第一行：主要开关
        row1 = ttk.Frame(parent)
        row1.pack(fill=tk.X, pady=(0, 8))

        # 启用规范化
        enable_var = tk.BooleanVar(value=self.normalize_settings.get('enable_normalization', True))
        setattr(self, f'norm_enable_var_{folder_id}', enable_var)
        enable_check = ttk.Checkbutton(row1, text="启用规范化", variable=enable_var)
        enable_check.pack(side=tk.LEFT, padx=(0, 15))

        # 应用替换规则
        replace_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_replace_var_{folder_id}', replace_var)
        replace_check = ttk.Checkbutton(row1, text="替换规则", variable=replace_var)
        replace_check.pack(side=tk.LEFT, padx=(0, 15))

        # 应用删除规则
        delete_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_delete_var_{folder_id}', delete_var)
        delete_check = ttk.Checkbutton(row1, text="删除规则", variable=delete_var)
        delete_check.pack(side=tk.LEFT)

        # 第二行：长度设置
        row2 = ttk.Frame(parent)
        row2.pack(fill=tk.X, pady=(0, 8))

        # 长度规范化
        length_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_length_var_{folder_id}', length_var)
        length_check = ttk.Checkbutton(row2, text="长度规范化", variable=length_var)
        length_check.pack(side=tk.LEFT, padx=(0, 10))

        # 长度范围
        ttk.Label(row2, text="范围:").pack(side=tk.LEFT, padx=(0, 2))
        min_var = tk.StringVar(value=str(self.normalize_settings.get('min_length', 25)))
        setattr(self, f'norm_min_var_{folder_id}', min_var)
        min_entry = ttk.Entry(row2, textvariable=min_var, width=3)
        min_entry.pack(side=tk.LEFT, padx=(0, 2))

        ttk.Label(row2, text="-").pack(side=tk.LEFT, padx=(0, 2))
        max_var = tk.StringVar(value=str(self.normalize_settings.get('max_length', 42)))
        setattr(self, f'norm_max_var_{folder_id}', max_var)
        max_entry = ttk.Entry(row2, textvariable=max_var, width=3)
        max_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 智能截断
        smart_var = tk.BooleanVar(value=self.normalize_settings.get('smart_truncate', True))
        setattr(self, f'norm_smart_var_{folder_id}', smart_var)
        smart_check = ttk.Checkbutton(row2, text="智能截断", variable=smart_var)
        smart_check.pack(side=tk.LEFT)

        # 第三行：处理重名和应用按钮
        row3 = ttk.Frame(parent)
        row3.pack(fill=tk.X, pady=(0, 5))

        # 处理重名
        duplicate_var = tk.BooleanVar(value=self.normalize_settings.get('auto_handle_duplicates', True))
        setattr(self, f'norm_duplicate_var_{folder_id}', duplicate_var)
        duplicate_check = ttk.Checkbutton(row3, text="处理重名", variable=duplicate_var)
        duplicate_check.pack(side=tk.LEFT, padx=(0, 20))

        # 应用规范化按钮
        apply_button = ttk.Button(row3, text="应用文件名规范化",
                                 command=lambda: self.apply_normalization_to_current_folder(operation, index))
        apply_button.pack(side=tk.RIGHT)

        # 状态显示
        row4 = ttk.Frame(parent)
        row4.pack(fill=tk.X)

        status_var = tk.StringVar(value="")
        setattr(self, f'norm_status_var_{folder_id}', status_var)
        status_label = ttk.Label(row4, textvariable=status_var,
                                foreground='green', font=('Arial', 9))
        status_label.pack(anchor=tk.W)

    def create_action_buttons_inside_settings(self, parent, current_operation):
        """在整理设置区域内部创建操作按钮"""
        # 添加一个小的分隔空间
        ttk.Label(parent, text="").pack(pady=(10, 0))

        # 第一行按钮：导航和重置（紧凑布局）
        row1 = ttk.Frame(parent)
        row1.pack(fill=tk.X, pady=(0, 3))

        # 返回上级按钮
        self.back_button = ttk.Button(row1, text="↑ 返回上级",
                                     command=self.go_back_to_parent)
        self.back_button.pack(side=tk.LEFT, padx=(0, 5))

        # 更新返回按钮状态
        self.update_back_button_state()

        # 上一个按钮
        if self.current_preview_index > 0:
            prev_button = ttk.Button(row1, text="← 上一个",
                                   command=self.prev_folder_preview)
            prev_button.pack(side=tk.LEFT, padx=(0, 5))

        # 重置为智能建议按钮
        reset_button = ttk.Button(row1, text="重置建议",
                                command=self.reset_current_folder)
        reset_button.pack(side=tk.LEFT, padx=(0, 5))

        # 下一个/完成预览按钮
        if self.current_preview_index < len(self.preview_operations) - 1:
            next_button = ttk.Button(row1, text="下一个 →",
                                   command=self.next_folder_preview)
            next_button.pack(side=tk.RIGHT)
        else:
            finish_button = ttk.Button(row1, text="完成预览",
                                     command=self.finish_preview)
            finish_button.pack(side=tk.RIGHT)

        # 第二行按钮：主要操作（紧凑布局）
        row2 = ttk.Frame(parent)
        row2.pack(fill=tk.X, pady=(0, 3))

        # 检查当前文件夹是否已经整理过
        is_organized = current_operation.get('is_organized', False)

        if is_organized:
            # 已整理的文件夹显示不同的按钮
            organized_button = ttk.Button(row2, text="✅ 已整理",
                                        state="disabled")
            organized_button.pack(side=tk.LEFT, padx=(0, 5))

            # 撤销整理按钮
            undo_button = ttk.Button(row2, text="↶ 撤销整理",
                                   command=self.undo_current_folder_operations)
            undo_button.pack(side=tk.LEFT, padx=(0, 5))
        else:
            # 未整理的文件夹显示应用按钮
            apply_button = ttk.Button(row2, text="✅ 应用整理",
                                    command=self.apply_and_organize_current_folder)
            apply_button.pack(side=tk.LEFT, padx=(0, 5))

            # 撤销更改按钮
            undo_button = ttk.Button(row2, text="↶ 撤销更改",
                                   command=self.undo_current_folder_changes)
            undo_button.pack(side=tk.LEFT, padx=(0, 5))

        # 取消按钮
        cancel_button = ttk.Button(row2, text="取消",
                                 command=self.close_preview_window)
        cancel_button.pack(side=tk.RIGHT)

    def create_normalization_settings_in_frame(self, parent, operation, index):
        """在规范化区域创建设置选项"""
        # 为每个文件夹创建独立的变量
        folder_id = f"folder_{index}"

        # 第一行：规则选项
        row1 = ttk.Frame(parent)
        row1.pack(fill=tk.X, pady=(0, 5))

        # 应用替换规则
        replace_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_replace_var_{folder_id}', replace_var)
        replace_check = ttk.Checkbutton(row1, text="替换规则", variable=replace_var)
        replace_check.pack(side=tk.LEFT, padx=(0, 20))

        # 应用删除规则
        delete_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_delete_var_{folder_id}', delete_var)
        delete_check = ttk.Checkbutton(row1, text="删除规则", variable=delete_var)
        delete_check.pack(side=tk.LEFT, padx=(0, 20))

        # 处理重名
        duplicate_var = tk.BooleanVar(value=self.normalize_settings.get('auto_handle_duplicates', True))
        setattr(self, f'norm_duplicate_var_{folder_id}', duplicate_var)
        duplicate_check = ttk.Checkbutton(row1, text="处理重名", variable=duplicate_var)
        duplicate_check.pack(side=tk.LEFT)

        # 第二行：长度设置
        row2 = ttk.Frame(parent)
        row2.pack(fill=tk.X, pady=(0, 5))

        # 长度规范化
        length_var = tk.BooleanVar(value=True)
        setattr(self, f'norm_length_var_{folder_id}', length_var)
        length_check = ttk.Checkbutton(row2, text="长度规范化", variable=length_var)
        length_check.pack(side=tk.LEFT, padx=(0, 10))

        # 长度范围
        ttk.Label(row2, text="范围:").pack(side=tk.LEFT, padx=(0, 5))
        min_var = tk.StringVar(value=str(self.normalize_settings.get('min_length', 25)))
        setattr(self, f'norm_min_var_{folder_id}', min_var)
        min_entry = ttk.Entry(row2, textvariable=min_var, width=3)
        min_entry.pack(side=tk.LEFT, padx=(0, 3))

        ttk.Label(row2, text="-").pack(side=tk.LEFT, padx=(0, 3))
        max_var = tk.StringVar(value=str(self.normalize_settings.get('max_length', 42)))
        setattr(self, f'norm_max_var_{folder_id}', max_var)
        max_entry = ttk.Entry(row2, textvariable=max_var, width=3)
        max_entry.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(row2, text="字符").pack(side=tk.LEFT)

        # 第三行：规则编辑按钮
        row3 = ttk.Frame(parent)
        row3.pack(fill=tk.X, pady=(5, 5))

        # 编辑替换规则按钮
        edit_replace_button = ttk.Button(row3, text="编辑替换规则",
                                        command=self.edit_replacement_rules)
        edit_replace_button.pack(side=tk.LEFT, padx=(0, 10))

        # 编辑删除规则按钮
        edit_delete_button = ttk.Button(row3, text="编辑删除规则",
                                       command=self.edit_deletion_rules)
        edit_delete_button.pack(side=tk.LEFT, padx=(0, 10))

        # 重载规则按钮
        reload_rules_button = ttk.Button(row3, text="重载规则",
                                        command=self.reload_normalization_rules)
        reload_rules_button.pack(side=tk.RIGHT)

        # 第四行：应用按钮和状态
        row4 = ttk.Frame(parent)
        row4.pack(fill=tk.X, pady=(5, 0))

        # 应用规范化按钮
        apply_button = ttk.Button(row4, text="应用文件名规范化",
                                 command=lambda: self.apply_normalization_to_current_folder(operation, index))
        apply_button.pack(side=tk.LEFT)

        # 状态显示
        status_var = tk.StringVar(value="")
        setattr(self, f'norm_status_var_{folder_id}', status_var)
        status_label = ttk.Label(row4, textvariable=status_var,
                                foreground='green', font=('Arial', 9))
        status_label.pack(side=tk.RIGHT)

    def apply_normalization_to_current_folder(self, operation, folder_index):
        """对当前文件夹应用文件名规范化"""
        folder_name = operation['folder_name']
        folder_path = operation['folder_path']
        folder_id = f"folder_{folder_index}"

        # 获取状态变量
        status_var = getattr(self, f'norm_status_var_{folder_id}', None)
        if status_var:
            status_var.set("🔄 正在应用规范化...")

        self.log_message(f"🔄 开始对文件夹 '{folder_name}' 应用文件名规范化")

        try:
            # 收集所有文件
            all_files = []

            # 添加各种类型的文件
            for file_info in operation.get('video_files', []):
                all_files.append(file_info)
            for file_info in operation.get('image_files', []):
                all_files.append(file_info)
            for file_info in operation.get('other_files', []):
                all_files.append(file_info)
            for file_info in operation.get('small_files', []):
                all_files.append(file_info)

            # 添加图片文件夹中的文件
            for folder_info in operation.get('image_folders', []):
                for file_info in folder_info.get('image_files', []):
                    all_files.append(file_info)

            if not all_files:
                self.log_message(f"⚠️ 文件夹 '{folder_name}' 中没有找到可规范化的文件")
                if status_var:
                    status_var.set("⚠️ 没有找到可处理的文件")
                return

            # 应用规范化
            normalized_count = self.normalize_files_list(all_files, folder_index)

            if normalized_count > 0:
                self.log_message(f"✅ 文件夹 '{folder_name}' 规范化完成，共处理 {normalized_count} 个文件")
                if status_var:
                    status_var.set(f"✅ 已处理 {normalized_count} 个文件")
            else:
                self.log_message(f"ℹ️ 文件夹 '{folder_name}' 中的文件名已符合规范，无需处理")
                if status_var:
                    status_var.set("ℹ️ 文件名已符合规范")

        except Exception as e:
            error_msg = f"❌ 规范化文件夹 '{folder_name}' 时出错: {str(e)}"
            self.log_message(error_msg)
            if status_var:
                status_var.set("❌ 处理时出错")

    def edit_replacement_rules(self):
        """编辑替换规则"""
        self.show_rules_editor("替换规则", "replacements.csv", "替换规则格式：原文本,新文本")

    def edit_deletion_rules(self):
        """编辑删除规则"""
        self.show_rules_editor("删除规则", "deletions.csv", "删除规则格式：每行一个要删除的文本")

    def show_rules_editor(self, title, filename, description):
        """显示规则编辑器对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"编辑{title}")
        dialog.geometry("600x500")
        dialog.configure(bg='#f0f0f0')
        dialog.transient(self.root)
        dialog.grab_set()

        # 保存状态跟踪
        is_saved = tk.BooleanVar(value=True)  # 初始状态为已保存
        original_content = ""  # 原始内容

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        def update_title():
            """更新窗口标题显示保存状态"""
            if is_saved.get():
                dialog.title(f"编辑{title}")
            else:
                dialog.title(f"编辑{title} *未保存*")

        def on_content_change(event=None):
            """内容变化时标记为未保存"""
            current_content = text_widget.get('1.0', tk.END).strip()
            if current_content != original_content:
                is_saved.set(False)
                update_title()

        # 主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题和说明
        title_label = ttk.Label(main_frame, text=f"编辑{title}",
                               font=('Microsoft YaHei', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        desc_label = ttk.Label(main_frame, text=description,
                              font=('Microsoft YaHei', 10),
                              foreground='blue')
        desc_label.pack(pady=(0, 15))

        # 文本编辑区域
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建文本框和滚动条
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定内容变化事件
        text_widget.bind('<KeyRelease>', on_content_change)
        text_widget.bind('<Button-1>', on_content_change)
        text_widget.bind('<Control-v>', lambda e: dialog.after(10, on_content_change))

        # 绑定内容变化事件
        text_widget.bind('<KeyRelease>', on_content_change)
        text_widget.bind('<Button-1>', on_content_change)
        text_widget.bind('<Control-v>', lambda e: dialog.after(10, on_content_change))  # 粘贴后延迟检查

        # 加载现有规则
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    text_widget.insert('1.0', content)
                    original_content = content.strip()  # 保存原始内容
            else:
                # 如果文件不存在，提供示例内容
                if filename == "replacements.csv":
                    sample_content = """# 替换规则示例 - 格式：原文本,新文本
# 清理常见的下载标记
[下载],
(下载),
【下载】,
_下载,
-下载,

# 清理网站标记
www.,
.com,
.net,

# 清理无用字符
_,
-,
【】,
(),

# 标准化词汇
高清,HD
超清,4K
蓝光,BluRay"""
                else:  # deletions.csv
                    sample_content = """# 删除规则示例 - 每行一个要删除的文本
# 删除广告标记
[广告]
【广告】
(广告)

# 删除下载来源
百度网盘
BaiduYun
115网盘

# 删除多余符号

..
--
__
【】
()
[]"""
                text_widget.insert('1.0', sample_content)
                original_content = sample_content.strip()  # 保存示例内容为原始内容
        except Exception as e:
            error_content = f"# 加载文件时出错: {str(e)}\n# 请手动输入规则\n"
            text_widget.insert('1.0', error_content)
            original_content = error_content.strip()

        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(10, 10))

        status_label = ttk.Label(status_frame, text="✅ 已加载规则文件",
                                foreground='green', font=('Arial', 9))
        status_label.pack(side=tk.LEFT)

        save_status_label = ttk.Label(status_frame, text="",
                                     font=('Arial', 9, 'bold'))
        save_status_label.pack(side=tk.RIGHT)

        def update_save_status():
            """更新保存状态显示"""
            if is_saved.get():
                save_status_label.config(text="✅ 已保存", foreground='green')
            else:
                save_status_label.config(text="⚠️ 未保存", foreground='red')

        # 初始状态显示
        update_save_status()

        # 重新定义内容变化处理函数，添加状态更新
        def on_content_change(event=None):
            """内容变化时标记为未保存"""
            current_content = text_widget.get('1.0', tk.END).strip()
            if current_content != original_content:
                is_saved.set(False)
                update_title()
                update_save_status()

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def save_rules():
            """保存规则到文件"""
            try:
                content = text_widget.get('1.0', tk.END).strip()

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)

                # 更新保存状态
                nonlocal original_content
                original_content = content
                is_saved.set(True)
                update_title()
                update_save_status()

                self.log_message(f"✅ {title}已保存到 {filename}")
                status_label.config(text="✅ 保存成功", foreground='green')

                # 自动重载规则
                self.reload_normalization_rules()

            except Exception as e:
                error_msg = f"保存{title}时出错：{str(e)}"
                self.log_message(f"❌ {error_msg}")
                status_label.config(text=f"❌ 保存失败: {str(e)}", foreground='red')
                messagebox.showerror("保存失败", error_msg)

        def save_and_close():
            """保存并关闭"""
            save_rules()
            if is_saved.get():  # 只有保存成功才关闭
                dialog.destroy()

        def cancel_edit():
            """取消编辑"""
            if not is_saved.get():
                # 如果有未保存的更改，询问用户
                result = messagebox.askyesnocancel(
                    "未保存的更改",
                    f"您对{title}有未保存的更改。\n\n是否要保存更改？",
                    parent=dialog
                )
                if result is True:  # 是 - 保存并关闭
                    save_rules()
                    if is_saved.get():
                        dialog.destroy()
                elif result is False:  # 否 - 不保存直接关闭
                    dialog.destroy()
                # None - 取消，不关闭窗口
            else:
                dialog.destroy()

        # 绑定窗口关闭事件
        dialog.protocol("WM_DELETE_WINDOW", cancel_edit)

        # 保存按钮
        save_button = ttk.Button(button_frame, text="保存", command=save_rules)
        save_button.pack(side=tk.LEFT, padx=(0, 10))

        # 保存并关闭按钮
        save_close_button = ttk.Button(button_frame, text="保存并关闭", command=save_and_close)
        save_close_button.pack(side=tk.LEFT, padx=(0, 10))

        # 关闭按钮
        close_button = ttk.Button(button_frame, text="关闭", command=cancel_edit)
        close_button.pack(side=tk.RIGHT)

    def reload_normalization_rules(self):
        """重新加载规范化规则"""
        try:
            # 重新加载规则
            replacements, deletions = self.load_normalization_rules()

            # 显示加载结果
            replace_count = len([r for r in replacements if r and len(r) >= 1 and r[0]])
            delete_count = len([d for d in deletions if d and len(d) >= 1 and d[0]])

            self.log_message(f"✅ 规则重载完成：替换规则 {replace_count} 条，删除规则 {delete_count} 条")

        except Exception as e:
            self.log_message(f"❌ 重载规则时出错: {str(e)}")


if __name__ == "__main__":
    app = FileOrganizer()
    app.run()
