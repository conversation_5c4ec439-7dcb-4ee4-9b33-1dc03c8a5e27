# -*- coding: utf-8 -*-
"""
测试文件名处理逻辑
"""
import os
import re

def has_triple_extension(filepath):
    """检查是否为三段式扩展名"""
    basename = os.path.basename(filepath)
    parts = basename.split('.')
    print(f"文件名: {basename}")
    print(f"分割后的部分: {parts}")
    print(f"部分数量: {len(parts)}")
    
    # 如果有4个或更多部分（文件名 + 3个扩展名），且最后三个都是扩展名格式
    if len(parts) >= 4:
        # 检查最后三个部分是否都像扩展名（长度2-4个字符，且不全是数字）
        last_three = parts[-3:]
        print(f"最后三个部分: {last_three}")
        for i, part in enumerate(last_three):
            print(f"  部分{i+1}: '{part}', 长度: {len(part)}")
            if len(part) < 2 or len(part) > 4:
                print(f"    -> 长度不符合要求")
                return False
        print("  -> 所有部分长度都符合要求")
        return True
    else:
        print("  -> 部分数量不足4个")
        return False

def test_filename(filename):
    print(f"\n{'='*50}")
    print(f"测试文件名: {filename}")
    print(f"{'='*50}")
    
    # 模拟 os.path.splitext
    base_filename, ext = os.path.splitext(filename)
    print(f"os.path.splitext 结果:")
    print(f"  filename: '{base_filename}'")
    print(f"  ext: '{ext}'")
    
    # 检查是否为三段式扩展名
    is_triple = has_triple_extension(filename)
    print(f"\n三段式扩展名检测结果: {is_triple}")
    
    # 检查扩展名是否在已知列表中
    known_extensions = {
        # 压缩文件
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
        # 视频文件
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
        # 音频文件
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
        # 图片文件
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
        # 文档文件
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
        # 程序文件
        '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
        # 其他常见文件
        '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
    }
    
    ext_in_known = ext.lower() in known_extensions
    print(f"扩展名 '{ext}' 在已知列表中: {ext_in_known}")
    
    # 最终判断条件
    condition = (not ext or ext.lower() not in known_extensions or is_triple)
    print(f"\n最终判断条件:")
    print(f"  not ext: {not ext}")
    print(f"  ext.lower() not in known_extensions: {ext.lower() not in known_extensions}")
    print(f"  is_triple_extension: {is_triple}")
    print(f"  最终结果 (会被视为未知扩展名): {condition}")

def create_test_files():
    """在 d:\1\ 目录中创建测试文件"""
    test_dir = r"d:\1"

    # 确保目录存在
    if not os.path.exists(test_dir):
        try:
            os.makedirs(test_dir)
            print(f"创建测试目录: {test_dir}")
        except Exception as e:
            print(f"无法创建测试目录 {test_dir}: {e}")
            return False

    # 测试文件列表
    test_files = [
        "L-叛逆.7z.005.avi",
        "L-MOONN.7z.003.zip",
        "L-MOONN.7z.003",
        "L-MOONN.7z",
        "normal_file.avi",
        "test.pdf.backup.zip",
        "large_file.unknown"
    ]

    print(f"\n在 {test_dir} 中创建测试文件...")

    for filename in test_files:
        file_path = os.path.join(test_dir, filename)
        try:
            # 创建大于500MB的文件用于测试
            if "large" in filename or "L-" in filename:
                # 使用 fsutil 创建大文件
                import subprocess
                size = 524288000  # 500MB
                result = subprocess.run([
                    "fsutil", "file", "createnew", file_path, str(size)
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    print(f"  ✓ 创建大文件: {filename} (500MB)")
                else:
                    print(f"  ✗ 创建大文件失败: {filename} - {result.stderr}")
            else:
                # 创建小文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("test content")
                print(f"  ✓ 创建小文件: {filename}")

        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

    return True

def test_directory_files():
    """测试目录中的实际文件"""
    test_dir = r"d:\1"

    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        return

    print(f"\n{'='*60}")
    print(f"测试目录中的实际文件: {test_dir}")
    print(f"{'='*60}")

    try:
        files = os.listdir(test_dir)
        if not files:
            print("目录为空")
            return

        for filename in files:
            file_path = os.path.join(test_dir, filename)
            if os.path.isfile(file_path):
                print(f"\n检测文件: {filename}")

                # 获取文件大小
                try:
                    size = os.path.getsize(file_path)
                    size_mb = size / (1024 * 1024)
                    print(f"  文件大小: {size_mb:.1f}MB")

                    # 检查是否为四段式扩展名
                    def has_four_part_extension(filepath):
                        basename = os.path.basename(filepath)
                        parts = basename.split('.')
                        if len(parts) >= 4:
                            last_three = parts[-3:]
                            for part in last_three:
                                if len(part) < 2 or len(part) > 4:
                                    return False
                            return True
                        return False

                    is_four_part = has_four_part_extension(file_path)
                    print(f"  四段式扩展名: {is_four_part}")

                    if size_mb >= 500 and is_four_part:
                        print(f"  ★ 此文件会触发四段式扩展名处理对话框")
                    elif size_mb >= 500:
                        print(f"  ○ 此文件大于等于500MB但不是四段式扩展名")
                    else:
                        print(f"  - 此文件不会触发处理（小于500MB）")

                except Exception as e:
                    print(f"  获取文件信息失败: {e}")

    except Exception as e:
        print(f"读取目录失败: {e}")

if __name__ == "__main__":
    print("后缀测试程序")
    print("=" * 50)

    # 创建测试文件
    if create_test_files():
        # 测试目录中的文件
        test_directory_files()

        print(f"\n{'='*60}")
        print("测试完成！")
        print("现在可以在解压缩GUI程序中选择 d:\\1\\ 目录")
        print("然后点击'规范文件后缀'按钮来测试四段式扩展名处理功能")
        print(f"{'='*60}")
    else:
        print("测试文件创建失败，无法继续测试")
