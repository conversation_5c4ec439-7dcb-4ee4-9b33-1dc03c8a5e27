# -*- coding: utf-8 -*-
"""
测试三段式扩展名的文件大小限制
"""
import os
import subprocess

def create_test_files():
    """创建不同大小的三段式扩展名测试文件"""
    test_dir = r"d:\1"
    
    # 确保目录存在
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试文件列表：(文件名, 大小MB, 描述)
    test_files = [
        ("small.7z.003.zip", 10, "小文件 - 不应该被处理"),
        ("medium.7z.003.zip", 60, "中等文件 - 应该被处理"),
        ("large.7z.003.zip", 100, "大文件 - 应该被处理"),
        ("tiny.pdf.backup.zip", 1, "极小文件 - 不应该被处理"),
        ("big.pdf.backup.zip", 80, "大文件 - 应该被处理"),
    ]
    
    print(f"在 {test_dir} 中创建不同大小的三段式扩展名测试文件...")
    
    for filename, size_mb, description in test_files:
        file_path = os.path.join(test_dir, filename)
        size_bytes = size_mb * 1024 * 1024
        
        try:
            # 使用 fsutil 创建指定大小的文件
            result = subprocess.run([
                "fsutil", "file", "createnew", file_path, str(size_bytes)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  ✓ 创建文件: {filename} ({size_mb}MB) - {description}")
            else:
                print(f"  ✗ 创建文件失败: {filename} - {result.stderr}")
                
        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

def test_triple_extension_logic():
    """测试三段式扩展名处理逻辑"""
    test_files = [
        ("small.7z.003.zip", 10),
        ("medium.7z.003.zip", 60), 
        ("large.7z.003.zip", 100),
        ("tiny.pdf.backup.zip", 1),
        ("big.pdf.backup.zip", 80),
        ("L-叛逆.7z.005.avi", 500),  # 四段式扩展名
        ("normal.file.txt", 5),      # 正常文件
    ]
    
    print(f"\n{'='*70}")
    print("测试三段式扩展名处理逻辑（50MB阈值）")
    print(f"{'='*70}")
    
    for filename, size_mb in test_files:
        print(f"\n测试文件: {filename} ({size_mb}MB)")
        
        # 检查是否为四段式扩展名
        parts = filename.split('.')
        is_four_part = False
        if len(parts) >= 4:
            last_three = parts[-3:]
            is_four_part = all(2 <= len(part) <= 4 for part in last_three)
        
        # 检查是否为三段式扩展名
        is_triple_extension = False
        if len(parts) >= 4:
            last_three = parts[-3:]
            is_triple_extension = all(2 <= len(part) <= 4 for part in last_three)
        
        # 模拟 os.path.splitext
        base_filename, ext = os.path.splitext(filename)
        
        # 检查扩展名是否在已知列表中
        known_extensions = {
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
            '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
            '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
        }
        
        is_unknown_extension = (not ext or ext.lower() not in known_extensions)
        is_large_triple_extension = (is_triple_extension and size_mb > 50)
        
        print(f"  四段式扩展名: {is_four_part}")
        print(f"  三段式扩展名: {is_triple_extension}")
        print(f"  文件大小: {size_mb}MB")
        print(f"  扩展名 '{ext}' 未知: {is_unknown_extension}")
        print(f"  大于50MB的三段式: {is_large_triple_extension}")
        
        # 判断处理结果
        if is_four_part:
            print(f"  → 会被四段式扩展名逻辑处理（不限制大小）")
        elif is_unknown_extension:
            if size_mb > 500:
                print(f"  → 会被未知扩展名逻辑处理（大于500MB）")
            else:
                print(f"  → 不会被处理（未知扩展名但小于500MB）")
        elif is_large_triple_extension:
            print(f"  → 会被三段式扩展名逻辑处理（大于50MB）")
        else:
            if is_triple_extension:
                print(f"  → 不会被处理（三段式扩展名但小于50MB）")
            else:
                print(f"  → 不会被处理（正常文件）")

if __name__ == "__main__":
    print("三段式扩展名大小限制测试")
    print("=" * 50)
    
    create_test_files()
    test_triple_extension_logic()
    
    print(f"\n{'='*70}")
    print("测试完成！")
    print("现在可以在解压缩GUI程序中选择 d:\\1\\ 目录")
    print("然后点击'规范文件后缀'按钮来测试三段式扩展名处理功能")
    print("只有大于50MB的三段式扩展名文件才会弹出重命名对话框")
    print(f"{'='*70}")
