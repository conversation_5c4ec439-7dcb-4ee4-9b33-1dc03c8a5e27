# -*- coding: utf-8 -*-
"""
测试包含汉字扩展名的文件处理
"""
import os

def create_test_files():
    """创建包含汉字扩展名的测试文件"""
    test_dir = r"d:\1"
    
    # 确保目录存在
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试文件列表
    test_files = [
        "视频1.7删z.001",
        "电影.压缩包.zip", 
        "文档.备份版.doc",
        "音乐.高清版.mp3",
        "软件.安装包.exe",
        "normal.file.txt"  # 正常文件作为对比
    ]
    
    print(f"在 {test_dir} 中创建包含汉字扩展名的测试文件...")
    
    for filename in test_files:
        file_path = os.path.join(test_dir, filename)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("test content for chinese extension")
            print(f"  ✓ 创建文件: {filename}")
        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

def test_chinese_extension_logic():
    """测试汉字扩展名处理逻辑"""
    test_files = [
        "视频1.7删z.001",
        "电影.压缩包.zip", 
        "文档.备份版.doc",
        "音乐.高清版.mp3",
        "软件.安装包.exe",
        "normal.file.txt",
        "L-叛逆.7z.005.avi"  # 四段式扩展名
    ]
    
    print(f"\n{'='*60}")
    print("测试汉字扩展名处理逻辑")
    print(f"{'='*60}")
    
    for filename in test_files:
        print(f"\n测试文件: {filename}")
        
        # 模拟 os.path.splitext
        base_filename, ext = os.path.splitext(filename)
        print(f"  os.path.splitext: filename='{base_filename}', ext='{ext}'")
        
        # 检查最后扩展名是否包含汉字
        has_chinese_in_ext = any('\u4e00' <= char <= '\u9fff' for char in ext)
        print(f"  最后扩展名包含汉字: {has_chinese_in_ext}")
        
        # 检查文件名中是否包含汉字扩展名
        if '.' in base_filename:
            parts = filename.split('.')
            print(f"  文件名分割: {parts}")
            
            has_chinese_extension = False
            chinese_parts = []
            for i, part in enumerate(parts[1:], 1):  # 跳过第一个部分（文件名）
                if any('\u4e00' <= char <= '\u9fff' for char in part):
                    has_chinese_extension = True
                    chinese_parts.append(f"部分{i}: '{part}'")
            
            print(f"  文件名中包含汉字扩展名: {has_chinese_extension}")
            if chinese_parts:
                print(f"  包含汉字的部分: {', '.join(chinese_parts)}")
            
            if has_chinese_extension:
                # 模拟清理过程
                new_parts = [parts[0]]  # 保留文件名部分
                for part in parts[1:]:
                    clean_part = ''.join(char for char in part if not ('\u4e00' <= char <= '\u9fff'))
                    if clean_part:
                        new_parts.append(clean_part)
                
                new_filename = '.'.join(new_parts)
                print(f"  清理后的文件名: {new_filename}")
        
        # 检查是否为四段式扩展名
        parts = filename.split('.')
        is_four_part = False
        if len(parts) >= 4:
            last_three = parts[-3:]
            is_four_part = all(2 <= len(part) <= 4 for part in last_three)
        
        print(f"  四段式扩展名: {is_four_part}")
        
        # 判断处理优先级
        if is_four_part:
            print(f"  → 会被四段式扩展名逻辑处理")
        elif has_chinese_in_ext:
            print(f"  → 会被最后扩展名汉字清理逻辑处理")
        elif '.' in base_filename:
            # 检查文件名中是否包含汉字扩展名
            parts = filename.split('.')
            has_chinese_in_filename = False
            for part in parts[1:-1]:  # 排除第一个文件名部分和最后一个扩展名部分
                if any('\u4e00' <= char <= '\u9fff' for char in part):
                    has_chinese_in_filename = True
                    break

            if has_chinese_in_filename:
                print(f"  → 会被文件名汉字扩展名清理逻辑处理")
            else:
                print(f"  → 不会被处理")
        else:
            print(f"  → 不会被处理")

if __name__ == "__main__":
    print("汉字扩展名处理测试")
    print("=" * 50)
    
    create_test_files()
    test_chinese_extension_logic()
    
    print(f"\n{'='*60}")
    print("测试完成！")
    print("现在可以在解压缩GUI程序中选择 d:\\1\\ 目录")
    print("然后点击'规范文件后缀'按钮来测试汉字扩展名处理功能")
    print(f"{'='*60}")
