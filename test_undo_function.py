# -*- coding: utf-8 -*-
"""
测试撤销功能
"""
import os
import subprocess

def create_test_files_for_undo():
    """创建用于测试撤销功能的文件"""
    test_dir = r"d:\1"
    
    # 确保目录存在
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 创建一些会被规范化的测试文件
    test_files = [
        # 包含汉字扩展名的文件（会被处理，不限制大小）
        ("测试文件.汉字扩展名", 1, "包含汉字扩展名"),
        ("视频.压缩包.zip", 1, "中间扩展名包含汉字"),
        ("文档.备份版.doc", 1, "中间扩展名包含汉字"),
        
        # 无扩展名的大文件（>100MB会被添加.7z）
        ("large_file_no_ext", 120, "无扩展名大文件"),
        ("another_big_file", 150, "无扩展名大文件"),
        
        # 需要标准化的扩展名
        ("test_file.7", 1, "需要标准化为.7z"),
        ("archive.7zz", 1, "需要标准化为.7z"),
        ("compressed.z", 1, "需要标准化为.7z"),
        
        # 四段式扩展名（>50MB会被处理）
        ("movie.avi.003.mp4", 80, "四段式扩展名"),
        ("data.7z.001.zip", 90, "四段式扩展名"),
        
        # 三段式扩展名（>50MB会被处理）
        ("video.mp4.bak", 70, "三段式扩展名"),
        ("document.pdf.old", 60, "三段式扩展名"),
    ]
    
    print(f"在 {test_dir} 中创建撤销功能测试文件...")
    
    for filename, size_mb, description in test_files:
        file_path = os.path.join(test_dir, filename)
        size_bytes = size_mb * 1024 * 1024
        
        try:
            if size_mb > 1:
                # 使用 fsutil 创建大文件
                result = subprocess.run([
                    "fsutil", "file", "createnew", file_path, str(size_bytes)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"  ✓ 创建文件: {filename} ({size_mb}MB) - {description}")
                else:
                    print(f"  ✗ 创建大文件失败: {filename} - {result.stderr}")
            else:
                # 创建小文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("test content for undo function")
                print(f"  ✓ 创建文件: {filename} (小文件) - {description}")
                
        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

def check_files_before_after():
    """检查文件处理前后的状态"""
    test_dir = r"d:\1"
    
    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        return
    
    print(f"\n{'='*70}")
    print(f"检查测试目录中的文件: {test_dir}")
    print(f"{'='*70}")
    
    try:
        files = os.listdir(test_dir)
        files.sort()
        
        if not files:
            print("目录为空")
            return
        
        for filename in files:
            file_path = os.path.join(test_dir, filename)
            if os.path.isfile(file_path):
                try:
                    size = os.path.getsize(file_path)
                    size_mb = size / (1024 * 1024)
                    print(f"  {filename} ({size_mb:.1f}MB)")
                except Exception as e:
                    print(f"  {filename} (无法获取大小: {e})")
                    
    except Exception as e:
        print(f"读取目录失败: {e}")

def analyze_expected_changes():
    """分析预期的文件变化"""
    print(f"\n{'='*70}")
    print("预期的文件变化分析：")
    print(f"{'='*70}")
    
    expected_changes = [
        ("测试文件.汉字扩展名", "测试文件.", "移除汉字扩展名"),
        ("视频.压缩包.zip", "视频.zip", "移除中间汉字扩展名"),
        ("文档.备份版.doc", "文档.doc", "移除中间汉字扩展名"),
        ("large_file_no_ext", "large_file_no_ext.7z", "添加.7z扩展名"),
        ("another_big_file", "another_big_file.7z", "添加.7z扩展名"),
        ("test_file.7", "test_file.7z", "标准化为.7z"),
        ("archive.7zz", "archive.7z", "标准化为.7z"),
        ("compressed.z", "compressed.7z", "标准化为.7z"),
        ("movie.avi.003.mp4", "弹出对话框", "四段式扩展名处理"),
        ("data.7z.001.zip", "弹出对话框", "四段式扩展名处理"),
        ("video.mp4.bak", "弹出对话框", "三段式扩展名处理"),
        ("document.pdf.old", "弹出对话框", "三段式扩展名处理"),
    ]
    
    for original, expected, description in expected_changes:
        print(f"  {original}")
        print(f"    → {expected}")
        print(f"    说明: {description}")
        print()

if __name__ == "__main__":
    print("撤销功能测试")
    print("=" * 50)
    
    create_test_files_for_undo()
    check_files_before_after()
    analyze_expected_changes()
    
    print(f"\n{'='*70}")
    print("测试步骤：")
    print("1. 在解压缩GUI程序中选择 d:\\1\\ 目录")
    print("2. 点击'规范文件后缀'按钮执行规范化")
    print("3. 观察文件名变化和日志输出")
    print("4. 点击'撤销重命名'按钮测试撤销功能")
    print("5. 验证文件名是否恢复到原始状态")
    print(f"{'='*70}")
    
    print("\n注意：")
    print("- 撤销按钮在规范化完成后才会启用")
    print("- 撤销操作会将所有重命名的文件恢复到原始状态")
    print("- 撤销后按钮会再次禁用，直到下次规范化操作")
