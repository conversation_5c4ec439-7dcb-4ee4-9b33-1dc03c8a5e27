# -*- coding: utf-8 -*-
"""
测试50MB限制对四段式和三段式扩展名的影响
"""
import os
import subprocess

def create_test_files():
    """创建不同大小的测试文件"""
    test_dir = r"d:\1"
    
    # 确保目录存在
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试文件列表：(文件名, 大小MB, 描述)
    test_files = [
        # 四段式扩展名
        ("small4.7z.001.zip", 30, "小四段式 - 不应该被处理（<50MB）"),
        ("large4.7z.001.zip", 80, "大四段式 - 应该被处理（>50MB）"),
        ("tiny4.avi.003.mp4", 10, "极小四段式 - 不应该被处理（<50MB）"),
        ("huge4.mkv.005.avi", 120, "巨大四段式 - 应该被处理（>50MB）"),
        
        # 三段式扩展名
        ("small3.mp4.bak", 20, "小三段式 - 不应该被处理（<50MB）"),
        ("large3.avi.old", 90, "大三段式 - 应该被处理（>50MB）"),
        ("tiny3.pdf.temp", 5, "极小三段式 - 不应该被处理（<50MB）"),
        ("huge3.doc.backup", 150, "巨大三段式 - 应该被处理（>50MB）"),
        
        # 正常文件
        ("normal.txt", 5, "正常小文件 - 不应该被处理"),
        ("big_normal.mp4", 200, "正常大文件 - 不应该被处理"),
    ]
    
    print(f"在 {test_dir} 中创建50MB限制测试文件...")
    
    for filename, size_mb, description in test_files:
        file_path = os.path.join(test_dir, filename)
        size_bytes = size_mb * 1024 * 1024
        
        try:
            # 使用 fsutil 创建指定大小的文件
            result = subprocess.run([
                "fsutil", "file", "createnew", file_path, str(size_bytes)
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  ✓ 创建文件: {filename} ({size_mb}MB) - {description}")
            else:
                print(f"  ✗ 创建文件失败: {filename} - {result.stderr}")
                
        except Exception as e:
            print(f"  ✗ 创建文件失败: {filename} - {e}")

def analyze_filename(filename):
    """分析文件名结构"""
    parts = filename.split('.')
    segment_count = len(parts)
    
    # 四段式：4个或更多部分，且最后三个都是2-4字符
    is_four_part = False
    if segment_count >= 4:
        last_three = parts[-3:]
        is_four_part = all(2 <= len(part) <= 4 for part in last_three)
    
    # 三段式：正好3个部分
    is_three_part = (segment_count == 3)
    
    return segment_count, is_four_part, is_three_part

def test_50mb_limit():
    """测试50MB限制逻辑"""
    test_files = [
        # 四段式扩展名
        ("small4.7z.001.zip", 30),
        ("large4.7z.001.zip", 80),
        ("tiny4.avi.003.mp4", 10),
        ("huge4.mkv.005.avi", 120),
        
        # 三段式扩展名
        ("small3.mp4.bak", 20),
        ("large3.avi.old", 90),
        ("tiny3.pdf.temp", 5),
        ("huge3.doc.backup", 150),
        
        # 正常文件
        ("normal.txt", 5),
        ("big_normal.mp4", 200),
    ]
    
    print(f"\n{'='*80}")
    print("测试50MB限制逻辑（四段式和三段式都要求>50MB）")
    print(f"{'='*80}")
    
    for filename, size_mb in test_files:
        print(f"\n测试文件: {filename} ({size_mb}MB)")
        
        segment_count, is_four_part, is_three_part = analyze_filename(filename)
        
        # 模拟 os.path.splitext
        base_filename, ext = os.path.splitext(filename)
        
        # 检查扩展名是否在已知列表中
        known_extensions = {
            '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.z',
            '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
            '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
            '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg', '.app',
            '.iso', '.img', '.bin', '.dat', '.log', '.cfg', '.ini'
        }
        
        is_unknown_extension = (not ext or ext.lower() not in known_extensions)
        is_large_enough = (size_mb > 50)
        
        print(f"  段数: {segment_count}")
        print(f"  四段式扩展名: {is_four_part}")
        print(f"  三段式扩展名: {is_three_part}")
        print(f"  最后扩展名 '{ext}': {'未知' if is_unknown_extension else '已知'}")
        print(f"  文件大小: {size_mb}MB (>50MB: {is_large_enough})")
        
        # 根据新的逻辑判断处理结果
        if is_four_part and is_large_enough:
            print(f"  → 会被四段式扩展名逻辑处理（>50MB）")
        elif is_four_part and not is_large_enough:
            print(f"  → 不会被处理（四段式但≤50MB）")
        elif is_three_part and is_large_enough:
            print(f"  → 会被三段式扩展名逻辑处理（>50MB）")
        elif is_three_part and not is_large_enough:
            print(f"  → 不会被处理（三段式但≤50MB）")
        elif is_unknown_extension and size_mb > 500:
            print(f"  → 会被未知扩展名逻辑处理（>500MB）")
        else:
            print(f"  → 不会被处理（正常文件或不满足条件）")

if __name__ == "__main__":
    print("50MB限制测试")
    print("=" * 50)
    
    create_test_files()
    test_50mb_limit()
    
    print(f"\n{'='*80}")
    print("测试总结（修改后的逻辑）：")
    print("- 四段式扩展名：需要大于50MB才处理")
    print("- 三段式扩展名：需要大于50MB才处理")
    print("- 未知扩展名：需要大于500MB才处理")
    print("- 正常文件：不处理")
    print(f"{'='*80}")
