#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新建文件夹功能
"""

import os
import tempfile
import shutil
from 网络媒体文件夹整理 import FileOrganizer

def test_create_new_folder():
    """测试新建文件夹功能"""
    print("开始测试新建文件夹功能...")
    
    # 创建临时测试目录
    test_dir = tempfile.mkdtemp(prefix="test_folder_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 创建一些测试文件和文件夹
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件")
        
        test_subfolder = os.path.join(test_dir, "子文件夹")
        os.makedirs(test_subfolder)
        
        print("测试目录结构创建完成")
        print(f"测试目录内容: {os.listdir(test_dir)}")
        
        # 创建FileOrganizer实例
        app = FileOrganizer()
        
        # 测试clean_filename方法
        print("\n测试文件名清理功能:")
        test_names = [
            "正常文件夹",
            "包含<>:非法字符的文件夹",
            "  前后有空格的文件夹  ",
            "文件夹.",
            "包含/\\|?*的文件夹"
        ]
        
        for name in test_names:
            cleaned = app.clean_filename(name)
            print(f"  '{name}' -> '{cleaned}'")
        
        print("\n✅ 新建文件夹功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_dir)
            print(f"清理测试目录: {test_dir}")
        except Exception as e:
            print(f"清理测试目录失败: {str(e)}")

if __name__ == "__main__":
    test_create_new_folder()
